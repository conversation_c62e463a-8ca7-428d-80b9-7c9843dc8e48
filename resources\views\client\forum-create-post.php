<?php if (!defined('IN_SITE')) {
    die('The Request Not Found');
}

// <PERSON><PERSON><PERSON> tra đăng nhập
if (isset($_COOKIE["token"])) {
    $getUser = $CMSNT->get_row(" SELECT * FROM `users` WHERE `token` = '" . check_string($_COOKIE['token']) . "' ");
    if (!$getUser) {
        // Clear invalid token cookie and redirect to login
        setcookie('token', '', time() - 3600, '/');
        redirect(base_url('client/login'));
        exit();
    }
    $_SESSION['login'] = $getUser['token'];
}
if (!isset($_SESSION['login'])) {
    redirect(base_url('client/login'));
}

$body = [
    'title' => '<PERSON><PERSON><PERSON> bà<PERSON> mới - <PERSON><PERSON> - ' . $CMSNT->site('title'),
    'desc'   => $CMSNT->site('description'),
    'keyword' => $CMSNT->site('keywords')
];

$body['header'] = '
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<style>
body {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    min-height: 100vh;
}

.content-page {
    background: transparent;
}

.create-post-hero {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    padding: 2rem 0;
    border-radius: 15px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 8px 32px rgba(52, 152, 219, 0.3);
}

.post-form-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    border: 2px solid #e9ecef;
    overflow: hidden;
}

.form-section {
    background: #ffffff;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 2px solid #f8f9fa;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.form-section h6 {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    font-size: 1.1rem;
}

.form-section h6 i {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
    padding: 0.5rem;
    border-radius: 50%;
    margin-right: 0.75rem;
    font-size: 0.875rem;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    background: white;
    color: #2c3e50;
    font-size: 1rem;
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    background: white;
}

.form-control::placeholder {
    color: #7f8c8d;
    opacity: 0.8;
}

.btn-create-post {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    color: white;
    font-weight: 700;
    transition: all 0.3s ease;
}

.btn-create-post:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.4);
    color: white;
}

.btn-cancel {
    background: linear-gradient(45deg, #95a5a6, #7f8c8d);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    color: white;
    font-weight: 700;
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(149, 165, 166, 0.4);
    color: white;
}

.sidebar-card {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border-radius: 15px;
    border: none;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.sidebar-card .card-header {
    background: rgba(255,255,255,0.15);
    border: none;
    border-radius: 15px 15px 0 0;
}

.sidebar-card .card-body {
    background: rgba(255,255,255,0.05);
}

.tips-card {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    border-radius: 15px;
    border: none;
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
}

.tips-card .card-header {
    background: rgba(255,255,255,0.15);
    border: none;
    border-radius: 15px 15px 0 0;
}

.tips-card .card-body {
    background: rgba(255,255,255,0.05);
}

.alert-posting-rules {
    background: linear-gradient(45deg, #e67e22, #d35400);
    color: white;
    border: none;
    border-radius: 10px;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(230, 126, 34, 0.3);
}

.breadcrumb {
    background: white;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.breadcrumb-item a {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
}

.breadcrumb-item.active {
    color: #2c3e50;
    font-weight: 600;
}

.image-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.image-preview img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

.upload-zone {
    border: 2px dashed #bdc3c7;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.upload-zone:hover {
    border-color: #3498db;
    background: #ecf0f1;
}

.upload-zone.dragover {
    border-color: #27ae60;
    background: #d5f4e6;
}

.char-counter {
    font-size: 0.875rem;
    color: #7f8c8d;
}

.char-counter.warning {
    color: #f39c12;
}

.char-counter.danger {
    color: #e74c3c;
}

#editor {
    height: 300px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
}

.ql-toolbar {
    border-top: 2px solid #e9ecef;
    border-left: 2px solid #e9ecef;
    border-right: 2px solid #e9ecef;
    border-bottom: 1px solid #e9ecef;
    border-radius: 8px 8px 0 0;
    background: #f8f9fa;
}

.ql-container {
    border-left: 2px solid #e9ecef;
    border-right: 2px solid #e9ecef;
    border-bottom: 2px solid #e9ecef;
    border-radius: 0 0 8px 8px;
    background: white;
}

.tag-input {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    min-height: 50px;
    cursor: text;
    background: white;
}

.tag {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
    padding: 0.35rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
}

.tag .remove-tag {
    cursor: pointer;
    font-weight: bold;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: background 0.3s ease;
}

.tag .remove-tag:hover {
    background: rgba(255,255,255,0.3);
}

.tag-input input {
    border: none;
    outline: none;
    flex: 1;
    min-width: 120px;
    background: transparent;
    color: #2c3e50;
    font-size: 1rem;
}

label {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.form-text {
    color: #6c757d;
    font-size: 0.875rem;
}
</style>';

$body['footer'] = '
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
';

$category_id = isset($_GET['category_id']) ? (int)$_GET['category_id'] : 0;
$categories = $CMSNT->get_list("SELECT * FROM forum_categories WHERE status = 1 ORDER BY position ASC");

require_once(__DIR__.'/header.php');
require_once(__DIR__.'/sidebar.php');
?>

<div class="content-page">
    <div class="content">
        <div class="container-fluid">
            <!-- Hero Section -->
            <div class="create-post-hero">
                <h1><i class="fas fa-pen-fancy mr-3"></i>✨ TẠO BÀI VIẾT MỚI ✨</h1>
                <p class="lead mb-0" style="font-size: 1.2rem; font-weight: 500;">
                    Chia sẻ kiến thức và trải nghiệm của bạn với cộng đồng ShareGameBai
                </p>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="post-form-card">
                        <div class="card-header" style="background: linear-gradient(45deg, #667eea, #764ba2);">
                            <h5 class="card-title mb-0" style="color:white;">
                                <i class="fas fa-edit mr-2"></i>Thông tin bài viết
                            </h5>
                        </div>
                        <div class="card-body p-4">
                            <!-- Breadcrumb -->
                            <nav aria-label="breadcrumb" class="mb-4">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item">
                                        <a href="?module=client&action=forum"><i class="fas fa-home mr-1"></i>Diễn đàn</a>
                                    </li>
                                    <li class="breadcrumb-item active">
                                        <i class="fas fa-plus mr-1"></i>Đăng bài mới
                                    </li>
                                </ol>
                            </nav>

                            <!-- Thông báo quy định -->
                            <div class="alert alert-posting-rules">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-info-circle fa-2x mr-3"></i>
                                    <div>
                                        <h6 class="mb-2"><strong>📋 QUY ĐỊNH ĐĂNG BÀI QUAN TRỌNG:</strong></h6>
                                        <p class="mb-0" style="line-height: 1.6;">
                                            <strong>• Giới hạn:</strong> Mỗi thành viên chỉ được đăng <strong>1 bài viết trong vòng 1 tuần</strong><br>
                                            <strong>• Duyệt bài:</strong> Bài viết sẽ được <strong>admin duyệt</strong> trước khi hiển thị công khai<br>
                                            <strong>• Vi phạm:</strong> Spam hoặc vi phạm nội quy sẽ bị xóa bài và cảnh cáo
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <form id="createPostForm" method="POST">
                                <!-- Thông tin cơ bản -->
                                <div class="form-section">
                                    <h6><i class="fas fa-info-circle"></i>Thông tin cơ bản</h6>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="category_id"><i class="fas fa-folder mr-1"></i>Danh mục <span class="text-danger">*</span></label>
                                                <select class="form-control" id="category_id" name="category_id" required>
                                                    <option value="">-- Chọn danh mục --</option>
                                                    <?php foreach($categories as $cat): ?>
                                                    <option value="<?=$cat['id'];?>" <?=$category_id == $cat['id'] ? 'selected' : '';?>>
                                                        <?=$cat['name'];?>
                                                    </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="title">
                                                    <i class="fas fa-heading mr-1"></i>Tiêu đề bài viết <span class="text-danger">*</span>
                                                </label>
                                                <input type="text" class="form-control" id="title" name="title" 
                                                       placeholder="Nhập tiêu đề bài viết..." maxlength="255" required>
                                                <div class="char-counter mt-1">
                                                    <span id="title-count">0</span>/255 ký tự
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="tags">
                                            <i class="fas fa-tags mr-1"></i>Tags (từ khóa)
                                        </label>
                                        <div class="tag-input" id="tagContainer">
                                            <input type="text" id="tagInput" placeholder="Nhập tag và nhấn Enter...">
                                        </div>
                                        <input type="hidden" id="tags" name="tags">
                                        <small class="text-muted">Nhập các từ khóa liên quan, mỗi tag nhấn Enter</small>
                                    </div>
                                </div>

                                <!-- Nội dung bài viết -->
                                <div class="form-section">
                                    <h6><i class="fas fa-file-alt"></i>Nội dung bài viết</h6>
                                    
                                    <div class="form-group">
                                        <label for="content">
                                            <i class="fas fa-edit mr-1"></i>Nội dung <span class="text-danger">*</span>
                                        </label>
                                        <div id="editor"></div>
                                        <textarea id="content" name="content" style="display: none;" required></textarea>
                                        <small class="text-muted">Sử dụng editor để định dạng nội dung một cách chuyên nghiệp</small>
                                    </div>
                                </div>

                                <!-- Hình ảnh và file đính kèm -->
                                <div class="form-section">
                                    <h6><i class="fas fa-images"></i>Hình ảnh & File đính kèm</h6>
                                    
                                    <div class="form-group">
                                        <label for="images">
                                            <i class="fas fa-image mr-1"></i>Hình ảnh (URL)
                                        </label>
                                        <textarea class="form-control" id="images" name="images" rows="3" 
                                                  placeholder="Nhập URL hình ảnh, mỗi URL một dòng...&#10;https://example.com/image1.jpg&#10;https://example.com/image2.png"></textarea>
                                        <small class="text-muted">Mỗi URL hình ảnh trên một dòng</small>
                                        <div id="imagePreview" class="image-preview"></div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="download_link">
                                                    <i class="fas fa-download mr-1"></i>Link tải xuống
                                                </label>
                                                <input type="url" class="form-control" id="download_link" name="download_link" 
                                                       placeholder="https://example.com/download">
                                                <small class="text-muted">Link file tải về (nếu có)</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="download_password">
                                                    <i class="fas fa-key mr-1"></i>Mật khẩu giải nén
                                                </label>
                                                <input type="text" class="form-control" id="download_password" name="download_password" 
                                                       placeholder="Mật khẩu file nén (nếu có)">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="is_link_hidden" 
                                               name="is_link_hidden" value="1" checked>
                                        <label class="form-check-label" for="is_link_hidden">
                                            <i class="fas fa-eye-slash mr-1"></i>Ẩn link tải cho đến khi có bình luận
                                        </label>
                                        <small class="form-text text-muted">Khuyến khích tương tác từ cộng đồng</small>
                                    </div>
                                </div>

                                <!-- Nút hành động -->
                                <div class="form-group text-center mt-4">
                                    <button type="submit" class="btn btn-create-post btn-lg">
                                        <i class="fas fa-paper-plane mr-2"></i>Đăng bài viết
                                    </button>
                                    <a href="<?=base_url('client/forum');?>" class="btn btn-cancel btn-lg ml-3">
                                        <i class="fas fa-times mr-2"></i>Hủy bỏ
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Tùy chọn bài viết -->
                    <div class="card sidebar-card">
                        <div class="card-header">
                            <h6 class="mb-0 text-white">
                                <i class="fas fa-cog mr-2"></i>Tùy chọn bài viết
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="notify_followers" 
                                       name="notify_followers" value="1">
                                <label class="form-check-label text-white" for="notify_followers">
                                    <i class="fas fa-bell mr-1"></i>Thông báo cho người theo dõi
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="allow_comments" 
                                       name="allow_comments" value="1" checked>
                                <label class="form-check-label text-white" for="allow_comments">
                                    <i class="fas fa-comments mr-1"></i>Cho phép bình luận
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="pin_request" 
                                       name="pin_request" value="1">
                                <label class="form-check-label text-white" for="pin_request">
                                    <i class="fas fa-thumbtack mr-1"></i>Yêu cầu ghim bài (Admin duyệt)
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Hướng dẫn đăng bài -->
                    <div class="card tips-card">
                        <div class="card-header">
                            <h6 class="mb-0 text-white">
                                <i class="fas fa-lightbulb mr-2"></i>Hướng dẫn đăng bài
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0 text-white">
                                <li class="mb-2"><i class="fas fa-check text-success mr-2"></i>Tiêu đề rõ ràng, thu hút</li>
                                <li class="mb-2"><i class="fas fa-check text-success mr-2"></i>Nội dung chi tiết, có giá trị</li>
                                <li class="mb-2"><i class="fas fa-check text-success mr-2"></i>Sử dụng hình ảnh minh họa</li>
                                <li class="mb-2"><i class="fas fa-check text-success mr-2"></i>Thêm tags phù hợp</li>
                                <li class="mb-2"><i class="fas fa-check text-success mr-2"></i>Link tải hoạt động tốt</li>
                                <li class="mb-2"><i class="fas fa-times text-warning mr-2"></i>Không spam hoặc vi phạm</li>
                                <li><i class="fas fa-info-circle text-info mr-2"></i>Bài viết cần admin duyệt</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Thống kê nhanh -->
                    <div class="card mt-3" style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white;">
                        <div class="card-header" style="background: rgba(255,255,255,0.1); border: none;">
                            <h6 class="mb-0 text-white">
                                <i class="fas fa-chart-bar mr-2"></i>Thống kê của bạn
                            </h6>
                        </div>
                        <div class="card-body" style="background: rgba(255,255,255,0.05);">
                            <?php
                            $userStats = $CMSNT->get_row("SELECT 
                                COUNT(*) as total_posts,
                                SUM(views) as total_views,
                                SUM(comments_count) as total_comments
                                FROM forum_posts 
                                WHERE user_id = '".$getUser['id']."'");
                            ?>
                            <div class="row text-center">
                                <div class="col-4">
                                    <h5 class="text-warning"><?=$userStats['total_posts'] ?? 0;?></h5>
                                    <small>Bài viết</small>
                                </div>
                                <div class="col-4">
                                    <h5 class="text-info"><?=number_format($userStats['total_views'] ?? 0);?></h5>
                                    <small>Lượt xem</small>
                                </div>
                                <div class="col-4">
                                    <h5 class="text-success"><?=$userStats['total_comments'] ?? 0;?></h5>
                                    <small>Bình luận</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Khởi tạo Quill Editor
var quill = new Quill('#editor', {
    theme: 'snow',
    placeholder: 'Viết nội dung bài viết của bạn...',
    modules: {
        toolbar: [
            [{ 'header': [1, 2, 3, false] }],
            ['bold', 'italic', 'underline', 'strike'],
            [{ 'color': [] }, { 'background': [] }],
            [{ 'align': [] }],
            ['blockquote', 'code-block'],
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            ['link', 'image'],
            ['clean']
        ]
    }
});

// Tag functionality
let tags = [];

function updateTagsInput() {
    $('#tags').val(tags.join(','));
}

function renderTags() {
    const container = $('#tagContainer');
    const input = container.find('input');
    container.find('.tag').remove();
    
    tags.forEach(tag => {
        const tagElement = $('<span class="tag">' + tag + '<span class="remove-tag">×</span></span>');
        tagElement.find('.remove-tag').click(function() {
            removeTag(tag);
        });
        input.before(tagElement);
    });
}

function addTag(tagText) {
    tagText = tagText.trim();
    if (tagText && !tags.includes(tagText) && tags.length < 10) {
        tags.push(tagText);
        renderTags();
        updateTagsInput();
    }
}

function removeTag(tagToRemove) {
    tags = tags.filter(tag => tag !== tagToRemove);
    renderTags();
    updateTagsInput();
}

$('#tagInput').on('keypress', function(e) {
    if (e.which === 13) { // Enter
        e.preventDefault();
        addTag($(this).val());
        $(this).val('');
    }
});

$('#tagContainer').click(function() {
    $('#tagInput').focus();
});

// Character counter for title
$('#title').on('input', function() {
    const length = $(this).val().length;
    const counter = $('#title-count');
    counter.text(length);
    
    if (length > 200) {
        counter.addClass('danger').removeClass('warning');
    } else if (length > 150) {
        counter.addClass('warning').removeClass('danger');
    } else {
        counter.removeClass('warning danger');
    }
});

// Image preview
$('#images').on('input', function() {
    const urls = $(this).val().split('\n').filter(url => url.trim());
    const preview = $('#imagePreview');
    preview.empty();
    
    urls.forEach(url => {
        url = url.trim();
        if (url) {
            const img = $('<img>').attr('src', url).on('error', function() {
                $(this).addClass('border-danger');
            });
            preview.append(img);
        }
    });
});

$(document).ready(function() {
    $('#createPostForm').on('submit', function(e) {
        e.preventDefault();
        
        // Lấy nội dung từ Quill editor
        const content = quill.root.innerHTML;
        $('#content').val(content);
        
        var formData = {
            category_id: $('#category_id').val(),
            title: $('#title').val(),
            content: content,
            images: $('#images').val(),
            download_link: $('#download_link').val(),
            download_password: $('#download_password').val(),
            is_link_hidden: $('#is_link_hidden').is(':checked') ? 1 : 0,
            tags: $('#tags').val(),
            notify_followers: $('#notify_followers').is(':checked') ? 1 : 0,
            allow_comments: $('#allow_comments').is(':checked') ? 1 : 0,
            pin_request: $('#pin_request').is(':checked') ? 1 : 0
        };

        // Validation
        if (!formData.category_id) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng chọn danh mục',
                timer: 3000
            });
            return;
        }

        if (!formData.title.trim()) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng nhập tiêu đề',
                timer: 3000
            });
            return;
        }

        if (formData.title.length > 255) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Tiêu đề không được vượt quá 255 ký tự',
                timer: 3000
            });
            return;
        }

        if (!content.trim() || content === '<p><br></p>') {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng nhập nội dung bài viết',
                timer: 3000
            });
            return;
        }

        // Disable submit button
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Đang đăng bài...');

        $.ajax({
            url: '<?=BASE_URL("ajaxs/client/forum-create-post.php");?>',
            type: 'POST',
            dataType: 'JSON',
            data: formData,
            beforeSend: function() {
                console.log('Sending data:', formData);
            },
            success: function(data) {
                console.log('Response:', data);
                if (data && data.status === 'success') {
                    // Clear draft khi thành công
                    localStorage.removeItem('forum_post_draft');

                    Swal.fire({
                        icon: 'success',
                        title: '🎉 Thành công!',
                        html: '<div style="text-align: center; font-size: 16px; line-height: 1.6;">' +
                              '<p><strong>' + data.message + '</strong></p>' +
                              '<div style="margin: 15px 0; padding: 10px; background: #e8f5e8; border-radius: 8px; border-left: 4px solid #28a745;">' +
                              '<i class="fas fa-info-circle text-success"></i> ' +
                              '<span style="color: #155724;">Bài viết của bạn đã được gửi thành công!</span>' +
                              '</div>' +
                              '</div>',
                        timer: 4000,
                        showConfirmButton: true,
                        confirmButtonText: 'Xem bài viết',
                        showCancelButton: true,
                        cancelButtonText: 'Về diễn đàn',
                        allowOutsideClick: false,
                        customClass: {
                            popup: 'animated bounceIn'
                        }
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Nút "Xem bài viết"
                            if (data.post_id) {
                                window.location.href = '?module=client&action=forum-post&id=' + data.post_id;
                            } else {
                                window.location.href = '?module=client&action=forum';
                            }
                        } else {
                            // Nút "Về diễn đàn" hoặc tự động sau 4s
                            window.location.href = '?module=client&action=forum';
                        }
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: '❌ Lỗi!',
                        html: '<div style="text-align: center;">' +
                              '<p><strong>' + (data ? data.message : 'Có lỗi xảy ra khi đăng bài') + '</strong></p>' +
                              '<div style="margin: 10px 0; padding: 8px; background: #f8d7da; border-radius: 5px; color: #721c24;">' +
                              '<i class="fas fa-exclamation-triangle"></i> Vui lòng kiểm tra lại thông tin và thử lại' +
                              '</div>' +
                              '</div>',
                        timer: 5000,
                        showConfirmButton: true,
                        confirmButtonText: 'Thử lại'
                    });
                    submitBtn.prop('disabled', false).html('<i class="fas fa-paper-plane mr-2"></i>Đăng bài viết');
                }
            },
            error: function(xhr, status, error) {
                console.log('AJAX Error Details:');
                console.log('XHR:', xhr);
                console.log('Response Text:', xhr.responseText);
                console.log('Status:', status);
                console.log('Error:', error);

                let errorMessage = 'Có lỗi xảy ra khi gửi bài viết.';
                if (xhr.responseText) {
                    try {
                        const errorData = JSON.parse(xhr.responseText);
                        if (errorData.message) {
                            errorMessage = errorData.message;
                        }
                    } catch (e) {
                        errorMessage = 'Lỗi kết nối đến server. Vui lòng thử lại.';
                    }
                }

                Swal.fire({
                    icon: 'error',
                    title: '🚫 Lỗi kết nối!',
                    html: '<div style="text-align: center;">' +
                          '<p><strong>' + errorMessage + '</strong></p>' +
                          '<div style="margin: 10px 0; padding: 8px; background: #f8d7da; border-radius: 5px; color: #721c24;">' +
                          '<i class="fas fa-wifi"></i> Kiểm tra kết nối mạng và thử lại' +
                          '</div>' +
                          '</div>',
                    timer: 6000,
                    showConfirmButton: true,
                    confirmButtonText: 'Thử lại'
                });
                submitBtn.prop('disabled', false).html('<i class="fas fa-paper-plane mr-2"></i>Đăng bài viết');
            }
        });
    });

    // Auto-save draft
    let draftTimer;
    function saveDraft() {
        const draft = {
            title: $('#title').val(),
            content: quill.root.innerHTML,
            category_id: $('#category_id').val(),
            tags: $('#tags').val()
        };
        localStorage.setItem('forum_post_draft', JSON.stringify(draft));
    }

    function loadDraft() {
        const draft = localStorage.getItem('forum_post_draft');
        if (draft) {
            try {
                const data = JSON.parse(draft);
                if (data.title) $('#title').val(data.title);
                if (data.content) quill.root.innerHTML = data.content;
                if (data.category_id) $('#category_id').val(data.category_id);
                if (data.tags) {
                    tags = data.tags.split(',').filter(tag => tag.trim());
                    renderTags();
                    updateTagsInput();
                }
            } catch (e) {
                console.log('Error loading draft:', e);
            }
        }
    }

    // Load draft on page load
    loadDraft();

    // Auto-save every 30 seconds
    $('#title, #category_id').on('input change', function() {
        clearTimeout(draftTimer);
        draftTimer = setTimeout(saveDraft, 2000);
    });

    quill.on('text-change', function() {
        clearTimeout(draftTimer);
        draftTimer = setTimeout(saveDraft, 2000);
    });

    // Clear draft on successful submit
    $('#createPostForm').on('submit', function() {
        localStorage.removeItem('forum_post_draft');
    });
});
</script>

<?php require_once(__DIR__ . '/footer.php'); ?>
