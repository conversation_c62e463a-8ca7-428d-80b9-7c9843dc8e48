0<?php
if (!defined('IN_SITE')) {
    die('The Request Not Found');
}

require_once(__DIR__.'/../../libs/db.php');
require_once(__DIR__.'/../../config.php');
require_once(__DIR__.'/../../libs/helper.php');

$CMSNT = new DB();

// Kiểm tra đăng nhập
if (isset($_COOKIE["token"])) {
    $getUser = $CMSNT->get_row(" SELECT * FROM `users` WHERE `token` = '" . check_string($_COOKIE['token']) . "' ");
    if (!$getUser) {
        die(json_encode(['status' => 'error', 'message' => 'Vui lòng đăng nhập']));
    }
} else {
    die(json_encode(['status' => 'error', 'message' => 'Vui lòng đăng nhập']));
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    die(json_encode(['status' => 'error', 'message' => 'Method not allowed']));
}

$post_id = (int)$_POST['post_id'];

// Kiểm tra bài viết có tồn tại
$post = $CMSNT->get_row("SELECT * FROM forum_posts WHERE id = '$post_id' AND status = 1");
if (!$post) {
    die(json_encode(['status' => 'error', 'message' => 'Bài viết không tồn tại']));
}

try {
    // Kiểm tra đã like chưa
    $existing_like = $CMSNT->get_row("SELECT * FROM forum_post_likes WHERE post_id = '$post_id' AND user_id = '".$getUser['id']."'");
    
    if ($existing_like) {
        // Đã like, bỏ like
        $CMSNT->remove('forum_post_likes', " post_id = '$post_id' AND user_id = '".$getUser['id']."' ");
        $new_likes = $post['likes'] - 1;
        $message = 'Đã bỏ thích bài viết';
    } else {
        // Chưa like, thêm like
        $CMSNT->insert('forum_post_likes', [
            'post_id' => $post_id,
            'user_id' => $getUser['id'],
            'created_at' => gmdate('Y-m-d H:i:s', time() + 7*3600)
        ]);
        $new_likes = $post['likes'] + 1;
        $message = 'Đã thích bài viết';
    }
    
    // Cập nhật số like trong bài viết
    $CMSNT->update('forum_posts', ['likes' => $new_likes], " id = '$post_id' ");
    
    die(json_encode([
        'status' => 'success',
        'message' => $message,
        'like_count' => $new_likes
    ]));
    
} catch (Exception $e) {
    die(json_encode(['status' => 'error', 'message' => 'Có lỗi xảy ra: ' . $e->getMessage()]));
}
?>
