<?php

require_once(__DIR__.'/../../config.php');
require_once(__DIR__.'/../../libs/db.php');
require_once(__DIR__.'/../../libs/helper.php');

$CMSNT = new DB();

// Set proper JSON headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Kiểm tra đăng nhập qua session hoặc cookie
if (!isset($_SESSION['login'])) {
    // Thử kiểm tra cookie
    if (isset($_COOKIE["token"])) {
        $getUser = $CMSNT->get_row(" SELECT * FROM `users` WHERE `token` = '".check_string($_COOKIE['token'])."' ");
        if ($getUser) {
            $_SESSION['login'] = $getUser['token'];
        } else {
            die(json_encode(['status' => 'error', 'message' => 'Vui lòng đăng nhập lại']));
        }
    } else {
        die(json_encode(['status' => 'error', 'message' => 'Vui lòng đăng nhập']));
    }
}

$getUser = $CMSNT->get_row(" SELECT * FROM `users` WHERE `token` = '".$_SESSION['login']."' ");
if (!$getUser) {
    die(json_encode(['status' => 'error', 'message' => 'Thông tin đăng nhập không hợp lệ']));
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    die(json_encode(['status' => 'error', 'message' => 'Method not allowed']));
}

$category_id = isset($_POST['category_id']) ? (int)$_POST['category_id'] : 0;
$title = isset($_POST['title']) ? trim($_POST['title']) : '';
$content = isset($_POST['content']) ? trim($_POST['content']) : '';
$images = isset($_POST['images']) ? trim($_POST['images']) : '';
$download_link = isset($_POST['download_link']) ? trim($_POST['download_link']) : '';
$download_password = isset($_POST['download_password']) ? trim($_POST['download_password']) : '';
$is_link_hidden = isset($_POST['is_link_hidden']) ? 1 : 0;
$tags = isset($_POST['tags']) ? trim($_POST['tags']) : '';
$notify_followers = isset($_POST['notify_followers']) ? 1 : 0;
$allow_comments = isset($_POST['allow_comments']) ? 1 : 0;
$pin_request = isset($_POST['pin_request']) ? 1 : 0;

// Validate dữ liệu
if (empty($category_id)) {
    die(json_encode(['status' => 'error', 'message' => 'Vui lòng chọn danh mục']));
}

if (empty($title)) {
    die(json_encode(['status' => 'error', 'message' => 'Vui lòng nhập tiêu đề']));
}

if (mb_strlen($title) > 255) {
    die(json_encode(['status' => 'error', 'message' => 'Tiêu đề không được vượt quá 255 ký tự']));
}

if (mb_strlen($content) > 50000) {
    die(json_encode(['status' => 'error', 'message' => 'Nội dung không được vượt quá 50,000 ký tự']));
}

// Kiểm tra danh mục có tồn tại
$category = $CMSNT->get_row("SELECT * FROM forum_categories WHERE id = '$category_id' AND status = 1");
if (!$category) {
    die(json_encode(['status' => 'error', 'message' => 'Danh mục không tồn tại']));
}

// Kiểm tra giới hạn đăng bài (trừ admin)
$isAdmin = isset($getUser['admin']) && $getUser['admin'] == 1;

if (!$isAdmin) {
    // Kiểm tra có phải bài viết đầu tiên không
    $total_posts = $CMSNT->get_row("SELECT COUNT(*) as total FROM forum_posts WHERE user_id = '".$getUser['id']."'")['total'];
    
    if ($total_posts > 0) {
        // Không phải bài đầu, kiểm tra điều kiện
        $last_post = $CMSNT->get_row("SELECT created_at FROM forum_posts WHERE user_id = '".$getUser['id']."' ORDER BY created_at DESC LIMIT 1");
        $last_post_time = strtotime($last_post['created_at']);
        $current_time = time();
        $time_diff = $current_time - $last_post_time;
        $one_week = 7 * 24 * 60 * 60; // 1 tuần tính bằng giây
        
        if ($time_diff < $one_week) {
            die(json_encode([
                'status' => 'error', 
                'message' => "Lưu ý: Mỗi thành viên chỉ được đăng 1 bài viết trong vòng 1 tuần. Admin không bị giới hạn này."
            ]));
        }
    }
}

// Làm sạch dữ liệu
$title = check_string($title);
$content = check_string($content);
$images = $images ? check_string($images) : null;
$download_link = $download_link ? check_string($download_link) : null;
$download_password = $download_password ? check_string($download_password) : null;
$tags = $tags ? check_string($tags) : null;

// Validate và xử lý tags
if ($tags) {
    $tag_array = explode(',', $tags);
    $valid_tags = [];
    foreach ($tag_array as $tag) {
        $tag = trim($tag);
        if ($tag && mb_strlen($tag) <= 50) {
            $valid_tags[] = $tag;
        }
    }
    if (count($valid_tags) > 10) {
        $valid_tags = array_slice($valid_tags, 0, 10);
    }
    $tags = !empty($valid_tags) ? implode(',', $valid_tags) : null;
}

// Validate URL download nếu có
if ($download_link && !filter_var($download_link, FILTER_VALIDATE_URL)) {
    die(json_encode(['status' => 'error', 'message' => 'Link tải không hợp lệ']));
}

// Validate URLs hình ảnh
if ($images) {
    $image_urls = explode("\n", $images);
    $valid_images = [];
    foreach ($image_urls as $url) {
        $url = trim($url);
        if ($url && filter_var($url, FILTER_VALIDATE_URL)) {
            $valid_images[] = $url;
        }
    }
    $images = !empty($valid_images) ? implode("\n", $valid_images) : null;
}

try {
    // Tạo bài viết mới - status = 0 để đợi admin duyệt (trừ admin)
    $post_status = $isAdmin ? 1 : 0; // Admin được duyệt tự động
    
    $insert = $CMSNT->insert('forum_posts', [
        'category_id' => $category_id,
        'user_id' => $getUser['id'],
        'title' => $title,
        'content' => $content,
        'images' => $images,
        'download_link' => $download_link,
        'download_password' => $download_password,
        'is_link_hidden' => $is_link_hidden,
        'tags' => $tags,
        'allow_comments' => $allow_comments,
        'status' => $post_status,
        'is_pinned' => 0, // Mặc định không ghim, admin có thể ghim sau
        'pin_requested' => $pin_request, // Lưu yêu cầu ghim để admin xem xét
        'created_at' => gmdate('Y-m-d H:i:s', time() + 7*3600),
        'updated_at' => gmdate('Y-m-d H:i:s', time() + 7*3600)
    ]);

    if ($insert) {
        $post_id = $CMSNT->lastInsertId();
        
        // Thông báo cho followers nếu được yêu cầu và user là admin
        if ($notify_followers && $isAdmin && $post_status == 1) {
            // Logic thông báo followers có thể thêm vào đây
            // Ví dụ: insert vào bảng notifications
        }
        
        if ($isAdmin) {
            $message = 'Đăng bài viết thành công!';
            if ($pin_request) {
                $message .= ' Bài viết đã được ghim tự động.';
                // Admin có thể tự ghim bài
                $CMSNT->update('forum_posts', ['is_pinned' => 1], "id = '$post_id'");
            }
        } else {
            $message = 'Đăng bài viết thành công! Vui lòng đợi admin duyệt bài viết của bạn.';
            if ($pin_request) {
                $message .= ' Yêu cầu ghim bài đã được gửi đến admin.';
            }
        }
        
        die(json_encode([
            'status' => 'success', 
            'message' => $message,
            'post_id' => $post_id,
            'redirect_url' => $isAdmin ? 
                '?module=client&action=forum-post&id='.$post_id : 
                '?module=client&action=forum'
        ]));
    } else {
        die(json_encode(['status' => 'error', 'message' => 'Có lỗi xảy ra khi đăng bài']));
    }
} catch (Exception $e) {
    die(json_encode(['status' => 'error', 'message' => 'Có lỗi xảy ra: ' . $e->getMessage()]));
}
?>
