{"version": 3, "sources": ["../../build/scss/parts/adminlte.extra-components.scss", "../../build/scss/mixins/_animations.scss", "../../build/scss/_small-box.scss", "../../node_modules/bootstrap/scss/mixins/_border-radius.scss", "../../node_modules/bootstrap/scss/mixins/_box-shadow.scss", "../../node_modules/bootstrap/scss/vendor/_rfs.scss", "../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "dist/css/alt/adminlte.extra-components.css", "../../build/scss/_info-box.scss", "../../build/scss/_timeline.scss", "../../build/scss/_products.scss", "../../node_modules/bootstrap/scss/mixins/_clearfix.scss", "../../build/scss/_direct-chat.scss", "../../build/scss/mixins/_miscellaneous.scss", "../../node_modules/bootstrap/scss/mixins/_lists.scss", "../../build/scss/mixins/_direct-chat.scss", "../../build/scss/_users-list.scss", "../../build/scss/_social-widgets.scss"], "names": [], "mappings": "AAAA;;;;;;ACKA,2BACE,GACE,kBAAA,mBAAA,sBAAA,UAAA,mBAAA,sBACA,2BAAA,QACA,QAAA,EAGF,IACE,kBAAA,mBAAA,uBAAA,UAAA,mBAAA,uBACA,2BAAA,QAGF,IACE,kBAAA,mBAAA,sBAAA,UAAA,mBAAA,sBACA,QAAA,EAGF,IACE,kBAAA,mBAAA,sBAAA,UAAA,mBAAA,sBAGF,KACE,kBAAA,mBAAA,UAAA,oBAtBJ,mBACE,GACE,kBAAA,mBAAA,sBAAA,UAAA,mBAAA,sBACA,2BAAA,QACA,QAAA,EAGF,IACE,kBAAA,mBAAA,uBAAA,UAAA,mBAAA,uBACA,2BAAA,QAGF,IACE,kBAAA,mBAAA,sBAAA,UAAA,mBAAA,sBACA,QAAA,EAGF,IACE,kBAAA,mBAAA,sBAAA,UAAA,mBAAA,sBAGF,KACE,kBAAA,mBAAA,UAAA,oBAKJ,0BACE,KACE,QAAA,EAGF,GACE,QAAA,GANJ,kBACE,KACE,QAAA,EAGF,GACE,QAAA,GAIJ,2BACE,KACE,QAAA,EAGF,GACE,QAAA,GANJ,mBACE,KACE,QAAA,EAGF,GACE,QAAA,GAIJ,yBACE,GACE,kBAAA,mBAAA,UAAA,UAAA,mBAAA,UAEF,IACE,kBAAA,qBAAA,cAAA,UAAA,qBAAA,cAEF,IACE,kBAAA,kBAAA,aAAA,UAAA,kBAAA,aAEF,IACE,kBAAA,iBAAA,UAAA,UAAA,iBAAA,UAEF,IACE,kBAAA,oBAAA,aAAA,UAAA,oBAAA,aAEF,IACE,kBAAA,oBAAA,cAAA,UAAA,oBAAA,cAEF,IACE,kBAAA,oBAAA,UAAA,UAAA,oBAAA,UAEF,IACE,kBAAA,mBAAA,cAAA,UAAA,mBAAA,cAEF,IACE,kBAAA,qBAAA,aAAA,UAAA,qBAAA,aAEF,IACE,kBAAA,mBAAA,UAAA,UAAA,mBAAA,UAEF,KACE,kBAAA,oBAAA,cAAA,UAAA,oBAAA,eAhCJ,iBACE,GACE,kBAAA,mBAAA,UAAA,UAAA,mBAAA,UAEF,IACE,kBAAA,qBAAA,cAAA,UAAA,qBAAA,cAEF,IACE,kBAAA,kBAAA,aAAA,UAAA,kBAAA,aAEF,IACE,kBAAA,iBAAA,UAAA,UAAA,iBAAA,UAEF,IACE,kBAAA,oBAAA,aAAA,UAAA,oBAAA,aAEF,IACE,kBAAA,oBAAA,cAAA,UAAA,oBAAA,cAEF,IACE,kBAAA,oBAAA,UAAA,UAAA,oBAAA,UAEF,IACE,kBAAA,mBAAA,cAAA,UAAA,mBAAA,cAEF,IACE,kBAAA,qBAAA,aAAA,UAAA,qBAAA,aAEF,IACE,kBAAA,mBAAA,UAAA,UAAA,mBAAA,UAEF,KACE,kBAAA,oBAAA,cAAA,UAAA,oBAAA,eAIJ,0BACE,GACE,kBAAA,KAAA,UAAA,KAGF,IACE,kBAAA,sBAAA,sBAAA,UAAA,sBAAA,sBAGF,IACE,kBAAA,qBAAA,qBAAA,UAAA,qBAAA,qBAGF,IACE,kBAAA,sBAAA,sBAAA,UAAA,sBAAA,sBAGF,IACE,kBAAA,qBAAA,qBAAA,UAAA,qBAAA,qBAGF,IACE,kBAAA,qBAAA,sBAAA,UAAA,qBAAA,sBAGF,KACE,kBAAA,KAAA,UAAA,MA1BJ,kBACE,GACE,kBAAA,KAAA,UAAA,KAGF,IACE,kBAAA,sBAAA,sBAAA,UAAA,sBAAA,sBAGF,IACE,kBAAA,qBAAA,qBAAA,UAAA,qBAAA,qBAGF,IACE,kBAAA,sBAAA,sBAAA,UAAA,sBAAA,sBAGF,IACE,kBAAA,qBAAA,qBAAA,UAAA,qBAAA,qBAGF,IACE,kBAAA,qBAAA,sBAAA,UAAA,qBAAA,sBAGF,KACE,kBAAA,KAAA,UAAA,MC9GJ,WCcI,cAAA,OCFE,WAAA,EAAA,EAAA,IAAA,gBAAA,CAAA,EAAA,IAAA,IAAA,eFRJ,QAAA,MACA,cAAA,KACA,SAAA,SANF,kBAUI,QAAA,KAVJ,6BAcI,iBAAA,eACA,MAAA,qBACA,QAAA,MACA,QAAA,IAAA,EACA,SAAA,SACA,WAAA,OACA,gBAAA,KACA,QAAA,GArBJ,mCAwBM,iBAAA,gBACA,MAAA,KAzBN,cG2HM,UAAA,OH5FF,YAAA,IACA,OAAA,EAAA,EAAA,KACA,QAAA,EACA,YAAA,OIsBA,yBCsOF,wBACA,wBLzPE,wBGqFE,UAAA,OEwKJ,wBACA,wBLtPE,wBG6EE,UAAA,QCnEF,0BCmPF,wBACA,wBLpPE,wBGmEE,UAAA,OEqLJ,wBACA,wBLjPE,wBG2DE,UAAA,QH3HN,aA0EI,UAAA,KA1EJ,mBA6EM,MAAA,QACA,QAAA,MACA,UAAA,MACA,WAAA,IAhFN,cKkUA,aL5OI,QAAA,EAtFJ,iBA2FI,MAAA,gBACA,QAAA,EA5FJ,mBA+FM,UAAA,KACA,SAAA,SACA,MAAA,KACA,IAAA,KACA,WAAA,kBAAA,IAAA,OAAA,WAAA,UAAA,IAAA,OAAA,WAAA,UAAA,IAAA,MAAA,CAAA,kBAAA,IAAA,OAnGN,sBAAA,uBAAA,uBAAA,uBAAA,uBAAA,uBAAA,uBA4GQ,UAAA,KACA,IAAA,KA7GR,qBAkHM,UAAA,KACA,SAAA,SACA,MAAA,KACA,IAAA,KACA,WAAA,kBAAA,IAAA,OAAA,WAAA,UAAA,IAAA,OAAA,WAAA,UAAA,IAAA,MAAA,CAAA,kBAAA,IAAA,OAtHN,iBA4HI,gBAAA,KA5HJ,yBAAA,4BAAA,6BAAA,6BAAA,6BAAA,6BAAA,6BAAA,6BAyIU,kBAAA,WAAA,UAAA,WAzIV,2BA6IQ,kBAAA,WAAA,UAAA,WIxEJ,4BJgFF,WACE,WAAA,OADF,iBAII,QAAA,KAJJ,aAQI,UAAA,MM7JN,UJYM,WAAA,EAAA,EAAA,IAAA,gBAAA,CAAA,EAAA,IAAA,IAAA,eDEF,cAAA,OKVF,iBAAA,KACA,QAAA,aAAA,QAAA,YAAA,QAAA,KACA,cAAA,KACA,WAAA,KACA,QAAA,MACA,SAAA,SACA,MAAA,KAVF,oBAaI,iBAAA,iBACA,OAAA,IACA,OAAA,IAAA,EAfJ,kCAkBM,iBAAA,KAlBN,yBAwBM,cAAA,OAGF,oBAAA,OAAA,eAAA,OAAA,YAAA,OACA,QAAA,aAAA,QAAA,YAAA,QAAA,KACA,UAAA,SACA,wBAAA,OAAA,cAAA,OAAA,gBAAA,OACA,WAAA,OACA,MAAA,KAhCJ,6BAmCM,UAAA,KAnCN,4BAwCI,QAAA,aAAA,QAAA,YAAA,QAAA,KACA,uBAAA,OAAA,mBAAA,OAAA,eAAA,OACA,wBAAA,OAAA,cAAA,OAAA,gBAAA,OACA,YAAA,IACA,aAAA,EAAA,SAAA,EAAA,KAAA,EACA,QAAA,EAAA,KA7CJ,2BAiDI,QAAA,MACA,WAAA,OACA,YAAA,ID+YJ,yBClcA,gCAwDI,QAAA,MACA,SAAA,OACA,cAAA,SACA,YAAA,OD+YJ,yCC1cA,gCAkEQ,MAAA,KD6YR,uDC/cA,8CAqEU,iBAAA,KD+YV,2CCpdA,kCAkEQ,MAAA,KDuZR,yDCzdA,gDAqEU,iBAAA,KDyZV,yCC9dA,gCAkEQ,MAAA,KDiaR,uDCneA,8CAqEU,iBAAA,KDmaV,sCCxeA,6BAkEQ,MAAA,KD2aR,oDC7eA,2CAqEU,iBAAA,KD6aV,yCClfA,gCAkEQ,MAAA,QDqbR,uDCvfA,8CAqEU,iBAAA,QArEV,+BD4fA,wCC1bQ,MAAA,KAlER,6CDigBA,sDC5bU,iBAAA,KDicV,uCCtgBA,8BAkEQ,MAAA,QDycR,qDC3gBA,4CAqEU,iBAAA,QArEV,6BDghBA,sCC9cQ,MAAA,KAlER,2CDqhBA,oDChdU,iBAAA,KArEV,yBA4EI,QAAA,MA5EJ,gCAgFI,OAAA,EFxBA,yBC2eF,0CACA,0CC/cE,0CAII,QAAA,KD+cN,0CACA,0CC5cE,0CAII,QAAA,MFzCJ,yBCwfF,0CACA,0CC1cE,0CHoBE,UAAA,OGfE,QAAA,MD0cN,0CACA,0CCvcE,0CHWE,UAAA,OGNE,QAAA,OF7DJ,0BCugBF,0CACA,0CCrcE,0CHAE,UAAA,KGKE,QAAA,MDqcN,0CACA,0CClcE,0CHTE,UAAA,KGcE,QAAA,OAMR,qBAEI,iBAAA,QACA,MAAA,KDgcJ,oDCncA,2CAQU,MAAA,KDgcV,kECxcA,yDAWY,iBAAA,KDkcZ,sDC7cA,6CAQU,MAAA,KD0cV,oECldA,2DAWY,iBAAA,KD4cZ,oDCvdA,2CAQU,MAAA,KDodV,kEC5dA,yDAWY,iBAAA,KDsdZ,iDCjeA,wCAQU,MAAA,KD8dV,+DCteA,sDAWY,iBAAA,KDgeZ,oDC3eA,2CAQU,MAAA,QDweV,kEChfA,yDAWY,iBAAA,QAXZ,0CDqfA,mDC7eU,MAAA,KARV,wDD0fA,iEC/eY,iBAAA,KDofZ,kDC/fA,yCAQU,MAAA,QD4fV,gECpgBA,uDAWY,iBAAA,QAXZ,wCDygBA,iDCjgBU,MAAA,KARV,sDD8gBA,+DCngBY,iBAAA,KC1JZ,UACE,OAAA,EAAA,EAAA,KACA,QAAA,EACA,SAAA,SAHF,kBNcI,cAAA,OMPA,iBAAA,QACA,OAAA,EACA,QAAA,GACA,KAAA,KACA,OAAA,EACA,SAAA,SACA,IAAA,EACA,MAAA,IAdJ,cAwBI,cAAA,KACA,aAAA,KACA,SAAA,SA1BJ,qBAAA,sBAoBM,QAAA,GACA,QAAA,MArBN,6BLYM,WAAA,EAAA,EAAA,IAAA,gBAAA,CAAA,EAAA,IAAA,IAAA,eDEF,cAAA,OMiBE,iBAAA,KACA,MAAA,QACA,YAAA,KACA,aAAA,KACA,WAAA,EACA,QAAA,EACA,SAAA,SArCN,mCAwCQ,MAAA,KACA,MAAA,MACA,UAAA,KACA,QAAA,KA3CR,8CA+CQ,cAAA,IAAA,MAAA,iBACA,MAAA,QACA,UAAA,KACA,YAAA,IACA,OAAA,EACA,QAAA,KApDR,gDAuDU,YAAA,IAvDV,4CF+tBA,8CElqBQ,QAAA,KA7DR,gDAkEU,OAAA,KFsqBV,+CACA,+CEzuBA,+CAuEU,OAAA,EAvEV,gDA6EU,MAAA,KA7EV,kBFovBA,mBAEA,mBADA,mBAFA,mBADA,mBAMA,mBADA,8BE7pBM,iBAAA,QACA,cAAA,IACA,UAAA,KACA,OAAA,KACA,KAAA,KACA,YAAA,KACA,SAAA,SACA,WAAA,OACA,IAAA,EACA,MAAA,KAnGN,8BAsGM,QAAA,IAtGN,2BNcI,cAAA,IM+FE,iBAAA,KACA,QAAA,aACA,YAAA,IACA,QAAA,IAKN,qCLzGM,WAAA,KK6GA,iBAAA,QACA,OAAA,IAAA,MAAA,QALN,sDAQQ,oBAAA,QAMR,6BAGM,iBAAA,QAHN,wCAMM,iBAAA,QACA,MAAA,KACA,aAAA,QARN,yDAWQ,MAAA,QACA,aAAA,QAZR,8CAeQ,MAAA,QClJR,eACE,WAAA,KACA,OAAA,EACA,QAAA,EAHF,qBPcI,cAAA,OOFA,iBAAA,KACA,QAAA,KAAA,EChBF,4BACE,QAAA,MACA,MAAA,KACA,QAAA,GDAJ,4BAkBI,MAAA,KAlBJ,gCAqBM,OAAA,KACA,MAAA,KAtBN,6BA2BI,YAAA,KA3BJ,8BA+BI,YAAA,IA/BJ,oCAmCI,MAAA,QACA,QAAA,MACA,SAAA,OACA,cAAA,SACA,YAAA,OAIJ,4BP7BI,cAAA,EO+BF,cAAA,IAAA,MAAA,iBAFF,yCAKI,oBAAA,EAKJ,gCAEI,iBAAA,QACA,MAAA,KACA,oBAAA,QAJJ,gCAQI,MAAA,QE7DJ,wBAEI,WAAA,OACA,QAAA,EACA,SAAA,SAJJ,kDCEE,kBAAA,eAAA,UAAA,eDFF,oDAgBM,MAAA,QAhBN,mDAsBM,MAAA,KAKN,sBCzBE,kBAAA,eAAA,UAAA,eD2BA,OAAA,MACA,SAAA,KACA,QAAA,KAGF,iBLs2BA,kBKp2BE,QAAA,MAGF,iBAEE,cAAA,KD5CA,wBACE,QAAA,MACA,MAAA,KACA,QAAA,GJu5BJ,sBK32BA,sBAEE,WAAA,kBAAA,IAAA,YAAA,WAAA,UAAA,IAAA,YAAA,WAAA,UAAA,IAAA,WAAA,CAAA,kBAAA,IAAA,YAGF,kBTnCI,cAAA,MSwCF,iBAAA,QACA,OAAA,IAAA,MAAA,QACA,MAAA,KACA,OAAA,IAAA,EAAA,EAAA,KACA,QAAA,IAAA,KACA,SAAA,SAVF,yBAAA,0BAeI,OAAA,MAAA,YACA,mBAAA,QACA,QAAA,IACA,OAAA,EACA,eAAA,KACA,SAAA,SACA,MAAA,KACA,IAAA,KACA,MAAA,EAvBJ,yBA2BI,aAAA,IACA,WAAA,KA5BJ,0BAgCI,aAAA,IACA,WAAA,KAGF,yBACE,YAAA,EACA,aAAA,KAFF,gCAAA,iCAMI,kBAAA,QACA,mBAAA,YACA,KAAA,KACA,MAAA,KAKN,iBTrFI,cAAA,ISuFF,MAAA,KACA,OAAA,KACA,MAAA,KAEA,wBACE,MAAA,MAIJ,mBACE,QAAA,MACA,UAAA,QACA,cAAA,IAGF,kBACE,YAAA,IAGF,uBACE,MAAA,QAIF,iDC3HE,kBAAA,eAAA,UAAA,eDiIF,sBCjIE,kBAAA,kBAAA,UAAA,kBDmIA,iBAAA,QACA,OAAA,EACA,MAAA,KACA,OAAA,MACA,SAAA,KACA,SAAA,SACA,IAAA,EACA,MAAA,KAGF,4BACE,iBAAA,QADF,gDAII,MAAA,QAJJ,gDAQI,MAAA,QARJ,+CAYI,MAAA,QAKJ,eEhKE,aAAA,EACA,WAAA,KF+JF,kBAKI,cAAA,IAAA,MAAA,eACA,OAAA,EACA,QAAA,KD1KF,yBACE,QAAA,MACA,MAAA,KACA,QAAA,GCgKJ,+BAUM,cAAA,EAKN,mBTjKI,cAAA,ISmKF,MAAA,KACA,MAAA,KAGF,oBACE,MAAA,KACA,YAAA,KAGF,oBL22BA,sBKz2BE,QAAA,MAGF,oBACE,YAAA,IAGF,sBACE,UAAA,QAGF,oBACE,MAAA,QACA,YAAA,IAGF,mBACE,MAAA,QG3MA,8CACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,qDAAA,sDAEE,kBAAA,QAPJ,gDACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,uDAAA,wDAEE,kBAAA,QAPJ,8CACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,qDAAA,sDAEE,kBAAA,QAPJ,2CACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,kDAAA,mDAEE,kBAAA,QAPJ,8CACE,iBAAA,QACA,aAAA,QACA,MAAA,QAEA,qDAAA,sDAEE,kBAAA,QAPJ,6CACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,oDAAA,qDAEE,kBAAA,QAPJ,4CACE,iBAAA,QACA,aAAA,QACA,MAAA,QAEA,mDAAA,oDAEE,kBAAA,QAPJ,2CACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,kDAAA,mDAEE,kBAAA,QAPJ,gDACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,uDAAA,wDAEE,kBAAA,QAPJ,2CACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,kDAAA,mDAEE,kBAAA,QAPJ,4CACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,mDAAA,oDAEE,kBAAA,QAPJ,2CACE,iBAAA,QACA,aAAA,QACA,MAAA,QAEA,kDAAA,mDAEE,kBAAA,QAPJ,8CACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,qDAAA,sDAEE,kBAAA,QAPJ,6CACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,oDAAA,qDAEE,kBAAA,QAPJ,2CACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,kDAAA,mDAEE,kBAAA,QAPJ,6CACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,oDAAA,qDAEE,kBAAA,QAPJ,6CACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,oDAAA,qDAEE,kBAAA,QAPJ,2CACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,kDAAA,mDAEE,kBAAA,QAPJ,0CACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,iDAAA,kDAEE,kBAAA,QAPJ,6CACE,iBAAA,QACA,aAAA,QACA,MAAA,QAEA,oDAAA,qDAEE,kBAAA,QAPJ,6CACE,iBAAA,QACA,aAAA,QACA,MAAA,QAEA,oDAAA,qDAEE,kBAAA,QAPJ,4CACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,mDAAA,oDAEE,kBAAA,QAPJ,2CACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,kDAAA,mDAEE,kBAAA,QAPJ,2CACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,kDAAA,mDAEE,kBAAA,QAPJ,4CACE,iBAAA,KACA,aAAA,KACA,MAAA,QAEA,mDAAA,oDAEE,kBAAA,KAPJ,2CACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,kDAAA,mDAEE,kBAAA,QAPJ,gDACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,uDAAA,wDAEE,kBAAA,QHoNN,6BAEI,iBAAA,QACA,aAAA,QACA,MAAA,KAJJ,oCAAA,qCAQM,mBAAA,QARN,kCAYI,MAAA,QAZJ,2CAAA,4CAiBM,mBAAA,YG5OJ,yDACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,gEAAA,iEAEE,kBAAA,QAPJ,2DACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,kEAAA,mEAEE,kBAAA,QAPJ,yDACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,gEAAA,iEAEE,kBAAA,QAPJ,sDACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,6DAAA,8DAEE,kBAAA,QAPJ,yDACE,iBAAA,QACA,aAAA,QACA,MAAA,QAEA,gEAAA,iEAEE,kBAAA,QAPJ,wDACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,+DAAA,gEAEE,kBAAA,QAPJ,uDACE,iBAAA,QACA,aAAA,QACA,MAAA,QAEA,8DAAA,+DAEE,kBAAA,QAPJ,sDACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,6DAAA,8DAEE,kBAAA,QAPJ,2DACE,iBAAA,QACA,aAAA,QACA,MAAA,QAEA,kEAAA,mEAEE,kBAAA,QAPJ,sDACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,6DAAA,8DAEE,kBAAA,QAPJ,uDACE,iBAAA,QACA,aAAA,QACA,MAAA,QAEA,8DAAA,+DAEE,kBAAA,QAPJ,sDACE,iBAAA,QACA,aAAA,QACA,MAAA,QAEA,6DAAA,8DAEE,kBAAA,QAPJ,yDACE,iBAAA,QACA,aAAA,QACA,MAAA,QAEA,gEAAA,iEAEE,kBAAA,QAPJ,wDACE,iBAAA,QACA,aAAA,QACA,MAAA,QAEA,+DAAA,gEAEE,kBAAA,QAPJ,sDACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,6DAAA,8DAEE,kBAAA,QAPJ,wDACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,+DAAA,gEAEE,kBAAA,QAPJ,wDACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,+DAAA,gEAEE,kBAAA,QAPJ,sDACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,6DAAA,8DAEE,kBAAA,QAPJ,qDACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,4DAAA,6DAEE,kBAAA,QAPJ,wDACE,iBAAA,QACA,aAAA,QACA,MAAA,QAEA,+DAAA,gEAEE,kBAAA,QAPJ,wDACE,iBAAA,QACA,aAAA,QACA,MAAA,QAEA,+DAAA,gEAEE,kBAAA,QAPJ,uDACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,8DAAA,+DAEE,kBAAA,QAPJ,sDACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,6DAAA,8DAEE,kBAAA,QAPJ,sDACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,6DAAA,8DAEE,kBAAA,QAPJ,uDACE,iBAAA,KACA,aAAA,KACA,MAAA,QAEA,8DAAA,+DAEE,kBAAA,KAPJ,sDACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,6DAAA,8DAEE,kBAAA,QAPJ,2DACE,iBAAA,QACA,aAAA,QACA,MAAA,KAEA,kEAAA,mEAEE,kBAAA,QCTN,YFAE,aAAA,EACA,WAAA,KEDF,eAII,MAAA,KACA,QAAA,KACA,WAAA,OACA,MAAA,IAPJ,mBbcI,cAAA,IaHE,OAAA,KACA,UAAA,KAZN,uBT2nDA,wCSzmDQ,MAAA,KT8mDR,iBSxmDA,iBAEE,QAAA,MAGF,iBACE,MAAA,QACA,UAAA,QACA,SAAA,OACA,cAAA,SACA,YAAA,OAGF,iBACE,MAAA,QACA,UAAA,KAGF,4BAEI,MAAA,QAFJ,4BAKI,MAAA,QC9CJ,aACE,OAAA,EACA,SAAA,SAIF,iCdgBI,uBAAA,OACA,wBAAA,OcTA,OAAA,MACA,QAAA,KACA,WAAA,OAVJ,mCAeI,UAAA,KACA,YAAA,IACA,cAAA,EACA,WAAA,EACA,YAAA,EAAA,IAAA,IAAA,eAnBJ,+BAwBI,WAAA,EAxBJ,gCA6BI,KAAA,IACA,YAAA,MACA,SAAA,SACA,IAAA,KAhCJ,oCAmCM,OAAA,IAAA,MAAA,KACA,OAAA,KACA,MAAA,KArCN,0BA0CI,YAAA,KAKJ,mCd/BI,uBAAA,OACA,wBAAA,OcmCA,QAAA,KALJ,qCAUI,UAAA,KACA,YAAA,IACA,cAAA,IACA,WAAA,IAbJ,iCAkBI,WAAA,EV6oDJ,iCU/pDA,qCAuBI,YAAA,KAvBJ,sCA6BM,MAAA,KACA,OAAA,KACA,MAAA", "sourcesContent": ["/*!\n *   AdminLTE v3.1.0\n *     Only Extra Components\n *   Author: Colorlib\n *   Website: AdminLTE.io <https://adminlte.io>\n *   License: Open source - MIT <https://opensource.org/licenses/MIT>\n */\n\n// Bootstrap\n// ---------------------------------------------------\n@import \"~bootstrap/scss/functions\";\n@import \"../bootstrap-variables\";\n@import \"~bootstrap/scss/mixins\";\n\n// Variables and Mixins\n// ---------------------------------------------------\n@import \"../variables\";\n@import \"../variables-alt\";\n@import \"../mixins\";\n\n@import \"extra-components\";\n", "//\n// Mixins: Animation\n//\n\n\n@keyframes flipInX {\n  0% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    transition-timing-function: ease-in;\n    opacity: 0;\n  }\n\n  40% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    transition-timing-function: ease-in;\n  }\n\n  60% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    opacity: 1;\n  }\n\n  80% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n  }\n\n  100% {\n    transform: perspective(400px);\n  }\n}\n\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes fadeOut {\n  from {\n    opacity: 1;\n  }\n\n  to {\n    opacity: 0;\n  }\n}\n\n@keyframes shake {\n  0% {\n    transform: translate(2px, 1px) rotate(0deg);\n  }\n  10% {\n    transform: translate(-1px, -2px) rotate(-2deg);\n  }\n  20% {\n    transform: translate(-3px, 0) rotate(3deg);\n  }\n  30% {\n    transform: translate(0, 2px) rotate(0deg);\n  }\n  40% {\n    transform: translate(1px, -1px) rotate(1deg);\n  }\n  50% {\n    transform: translate(-1px, 2px) rotate(-1deg);\n  }\n  60% {\n    transform: translate(-3px, 1px) rotate(0deg);\n  }\n  70% {\n    transform: translate(2px, 1px) rotate(-2deg);\n  }\n  80% {\n    transform: translate(-1px, -1px) rotate(4deg);\n  }\n  90% {\n    transform: translate(2px, 2px) rotate(0deg);\n  }\n  100% {\n    transform: translate(1px, -2px) rotate(-1deg);\n  }\n}\n\n@keyframes wobble {\n  0% {\n    transform: none;\n  }\n\n  15% {\n    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\n  }\n\n  30% {\n    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\n  }\n\n  45% {\n    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\n  }\n\n  60% {\n    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\n  }\n\n  75% {\n    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\n  }\n\n  100% {\n    transform: none;\n  }\n}\n\n//\n", "//\n// Component: Small Box\n//\n\n.small-box {\n  @include border-radius($border-radius);\n  @include box-shadow($card-shadow);\n\n  display: block;\n  margin-bottom: 20px;\n  position: relative;\n\n  // content wrapper\n  > .inner {\n    padding: 10px;\n  }\n\n  > .small-box-footer {\n    background-color: rgba($black, .1);\n    color: rgba($white, .8);\n    display: block;\n    padding: 3px 0;\n    position: relative;\n    text-align: center;\n    text-decoration: none;\n    z-index: 10;\n\n    &:hover {\n      background-color: rgba($black, .15);\n      color: $white;\n    }\n  }\n\n  h3 {\n    @include font-size(2.2rem);\n    font-weight: 700;\n    margin: 0 0 10px;\n    padding: 0;\n    white-space: nowrap;\n  }\n\n  @include media-breakpoint-up(lg) {\n    .col-xl-2 &,\n    .col-lg-2 &,\n    .col-md-2 & {\n      h3 {\n        @include font-size(1.6rem);\n      }\n    }\n\n    .col-xl-3 &,\n    .col-lg-3 &,\n    .col-md-3 & {\n      h3 {\n        @include font-size(1.6rem);\n      }\n    }\n  }\n\n  @include media-breakpoint-up(xl) {\n    .col-xl-2 &,\n    .col-lg-2 &,\n    .col-md-2 & {\n      h3 {\n        @include font-size(2.2rem);\n      }\n    }\n\n    .col-xl-3 &,\n    .col-lg-3 &,\n    .col-md-3 & {\n      h3 {\n        @include font-size(2.2rem);\n      }\n    }\n  }\n\n  p {\n    font-size: 1rem;\n\n    > small {\n      color: $gray-100;\n      display: block;\n      font-size: .9rem;\n      margin-top: 5px;\n    }\n  }\n\n  h3,\n  p {\n    z-index: 5;\n  }\n\n  // the icon\n  .icon {\n    color: rgba($black, .15);\n    z-index: 0;\n\n    > i {\n      font-size: 90px;\n      position: absolute;\n      right: 15px;\n      top: 15px;\n      transition: transform $transition-speed linear;\n\n      &.fa,\n      &.fas,\n      &.far,\n      &.fab,\n      &.fal,\n      &.fad,\n      &.ion {\n        font-size: 70px;\n        top: 20px;\n      }\n    }\n\n    svg {\n      font-size: 70px;\n      position: absolute;\n      right: 15px;\n      top: 15px;\n      transition: transform $transition-speed linear;\n    }\n  }\n\n  // Small box hover state\n  &:hover {\n    text-decoration: none;\n\n    // Animate icons on small box hover\n    .icon {\n      > i {\n        &,\n        &.fa,\n        &.fas,\n        &.far,\n        &.fab,\n        &.fal,\n        &.fad,\n        &.ion {\n          transform: scale(1.1);\n        }\n      }\n      > svg {\n        transform: scale(1.1);\n      }\n    }\n  }\n}\n\n@include media-breakpoint-down(sm) {\n  // No need for icons on very small devices\n  .small-box {\n    text-align: center;\n\n    .icon {\n      display: none;\n    }\n\n    p {\n      font-size: 12px;\n    }\n  }\n}\n", "// stylelint-disable property-disallowed-list\n// Single side border-radius\n\n// Helper function to replace negative values with 0\n@function valid-radius($radius) {\n  $return: ();\n  @each $value in $radius {\n    @if type-of($value) == number {\n      $return: append($return, max($value, 0));\n    } @else {\n      $return: append($return, $value);\n    }\n  }\n  @return $return;\n}\n\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: valid-radius($radius);\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-right-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-left-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n", "@mixin box-shadow($shadow...) {\n  @if $enable-shadows {\n    $result: ();\n\n    @if (length($shadow) == 1) {\n      // We can pass `@include box-shadow(none);`\n      $result: $shadow;\n    } @else {\n      // Filter to avoid invalid properties for example `box-shadow: none, 1px 1px black;`\n      @for $i from 1 through length($shadow) {\n        @if nth($shadow, $i) != \"none\" {\n          $result: append($result, nth($shadow, $i), \"comma\");\n        }\n      }\n    }\n    @if (length($result) > 0) {\n      box-shadow: $result;\n    }\n  }\n}\n", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated font-resizing\n//\n// See https://github.com/twbs/rfs\n\n// Configuration\n\n// Base font size\n$rfs-base-font-size: 1.25rem !default;\n$rfs-font-size-unit: rem !default;\n\n// Breakpoint at where font-size starts decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n// Resize font-size based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != \"number\" or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-responsive-font-sizes to false\n$enable-responsive-font-sizes: true !default;\n\n// Cache $rfs-base-font-size unit\n$rfs-base-font-size-unit: unit($rfs-base-font-size);\n\n// Remove px-unit from $rfs-base-font-size for calculations\n@if $rfs-base-font-size-unit == \"px\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1);\n}\n@else if $rfs-base-font-size-unit == \"rem\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1 / $rfs-rem-value);\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == \"px\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == \"rem\" or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1 / $rfs-rem-value);\n}\n\n// Responsive font-size mixin\n@mixin rfs($fs, $important: false) {\n  // Cache $fs unit\n  $fs-unit: if(type-of($fs) == \"number\", unit($fs), false);\n\n  // Add !important suffix if needed\n  $rfs-suffix: if($important, \" !important\", \"\");\n\n  // If $fs isn't a number (like inherit) or $fs has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n  @if not $fs-unit or $fs-unit != \"\" and $fs-unit != \"px\" and $fs-unit != \"rem\" or $fs == 0 {\n    font-size: #{$fs}#{$rfs-suffix};\n  }\n  @else {\n    // Variables for storing static and fluid rescaling\n    $rfs-static: null;\n    $rfs-fluid: null;\n\n    // Remove px-unit from $fs for calculations\n    @if $fs-unit == \"px\" {\n      $fs: $fs / ($fs * 0 + 1);\n    }\n    @else if $fs-unit == \"rem\" {\n      $fs: $fs / ($fs * 0 + 1 / $rfs-rem-value);\n    }\n\n    // Set default font-size\n    @if $rfs-font-size-unit == rem {\n      $rfs-static: #{$fs / $rfs-rem-value}rem#{$rfs-suffix};\n    }\n    @else if $rfs-font-size-unit == px {\n      $rfs-static: #{$fs}px#{$rfs-suffix};\n    }\n    @else {\n      @error \"`#{$rfs-font-size-unit}` is not a valid unit for $rfs-font-size-unit. Use `px` or `rem`.\";\n    }\n\n    // Only add media query if font-size is bigger as the minimum font-size\n    // If $rfs-factor == 1, no rescaling will take place\n    @if $fs > $rfs-base-font-size and $enable-responsive-font-sizes {\n      $min-width: null;\n      $variable-unit: null;\n\n      // Calculate minimum font-size for given font-size\n      $fs-min: $rfs-base-font-size + ($fs - $rfs-base-font-size) / $rfs-factor;\n\n      // Calculate difference between given font-size and minimum font-size for given font-size\n      $fs-diff: $fs - $fs-min;\n\n      // Base font-size formatting\n      // No need to check if the unit is valid, because we did that before\n      $min-width: if($rfs-font-size-unit == rem, #{$fs-min / $rfs-rem-value}rem, #{$fs-min}px);\n\n      // If two-dimensional, use smallest of screen width and height\n      $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n      // Calculate the variable width between 0 and $rfs-breakpoint\n      $variable-width: #{$fs-diff * 100 / $rfs-breakpoint}#{$variable-unit};\n\n      // Set the calculated font-size.\n      $rfs-fluid: calc(#{$min-width} + #{$variable-width}) #{$rfs-suffix};\n    }\n\n    // Rendering\n    @if $rfs-fluid == null {\n      // Only render static font-size if no fluid font-size is available\n      font-size: $rfs-static;\n    }\n    @else {\n      $mq-value: null;\n\n      // RFS breakpoint formatting\n      @if $rfs-breakpoint-unit == em or $rfs-breakpoint-unit == rem {\n        $mq-value: #{$rfs-breakpoint / $rfs-rem-value}#{$rfs-breakpoint-unit};\n      }\n      @else if $rfs-breakpoint-unit == px {\n        $mq-value: #{$rfs-breakpoint}px;\n      }\n      @else {\n        @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n      }\n\n      @if $rfs-class == \"disable\" {\n        // Adding an extra class increases specificity,\n        // which prevents the media query to override the font size\n        &,\n        .disable-responsive-font-size &,\n        &.disable-responsive-font-size {\n          font-size: $rfs-static;\n        }\n      }\n      @else {\n        font-size: $rfs-static;\n      }\n\n      @if $rfs-two-dimensional {\n        @media (max-width: #{$mq-value}), (max-height: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n      @else {\n        @media (max-width: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n    }\n  }\n}\n\n// The font-size & responsive-font-size mixin uses RFS to rescale font sizes\n@mixin font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n\n@mixin responsive-font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "/*!\n *   AdminLTE v3.1.0\n *     Only Extra Components\n *   Author: Colorlib\n *   Website: AdminLTE.io <https://adminlte.io>\n *   License: Open source - MIT <https://opensource.org/licenses/MIT>\n */\n@-webkit-keyframes flipInX {\n  0% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    transition-timing-function: ease-in;\n    opacity: 0;\n  }\n  40% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    transition-timing-function: ease-in;\n  }\n  60% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    opacity: 1;\n  }\n  80% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n  }\n  100% {\n    -webkit-transform: perspective(400px);\n    transform: perspective(400px);\n  }\n}\n@keyframes flipInX {\n  0% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    transition-timing-function: ease-in;\n    opacity: 0;\n  }\n  40% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    transition-timing-function: ease-in;\n  }\n  60% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    opacity: 1;\n  }\n  80% {\n    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n  }\n  100% {\n    -webkit-transform: perspective(400px);\n    transform: perspective(400px);\n  }\n}\n\n@-webkit-keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@-webkit-keyframes fadeOut {\n  from {\n    opacity: 1;\n  }\n  to {\n    opacity: 0;\n  }\n}\n\n@keyframes fadeOut {\n  from {\n    opacity: 1;\n  }\n  to {\n    opacity: 0;\n  }\n}\n\n@-webkit-keyframes shake {\n  0% {\n    -webkit-transform: translate(2px, 1px) rotate(0deg);\n    transform: translate(2px, 1px) rotate(0deg);\n  }\n  10% {\n    -webkit-transform: translate(-1px, -2px) rotate(-2deg);\n    transform: translate(-1px, -2px) rotate(-2deg);\n  }\n  20% {\n    -webkit-transform: translate(-3px, 0) rotate(3deg);\n    transform: translate(-3px, 0) rotate(3deg);\n  }\n  30% {\n    -webkit-transform: translate(0, 2px) rotate(0deg);\n    transform: translate(0, 2px) rotate(0deg);\n  }\n  40% {\n    -webkit-transform: translate(1px, -1px) rotate(1deg);\n    transform: translate(1px, -1px) rotate(1deg);\n  }\n  50% {\n    -webkit-transform: translate(-1px, 2px) rotate(-1deg);\n    transform: translate(-1px, 2px) rotate(-1deg);\n  }\n  60% {\n    -webkit-transform: translate(-3px, 1px) rotate(0deg);\n    transform: translate(-3px, 1px) rotate(0deg);\n  }\n  70% {\n    -webkit-transform: translate(2px, 1px) rotate(-2deg);\n    transform: translate(2px, 1px) rotate(-2deg);\n  }\n  80% {\n    -webkit-transform: translate(-1px, -1px) rotate(4deg);\n    transform: translate(-1px, -1px) rotate(4deg);\n  }\n  90% {\n    -webkit-transform: translate(2px, 2px) rotate(0deg);\n    transform: translate(2px, 2px) rotate(0deg);\n  }\n  100% {\n    -webkit-transform: translate(1px, -2px) rotate(-1deg);\n    transform: translate(1px, -2px) rotate(-1deg);\n  }\n}\n\n@keyframes shake {\n  0% {\n    -webkit-transform: translate(2px, 1px) rotate(0deg);\n    transform: translate(2px, 1px) rotate(0deg);\n  }\n  10% {\n    -webkit-transform: translate(-1px, -2px) rotate(-2deg);\n    transform: translate(-1px, -2px) rotate(-2deg);\n  }\n  20% {\n    -webkit-transform: translate(-3px, 0) rotate(3deg);\n    transform: translate(-3px, 0) rotate(3deg);\n  }\n  30% {\n    -webkit-transform: translate(0, 2px) rotate(0deg);\n    transform: translate(0, 2px) rotate(0deg);\n  }\n  40% {\n    -webkit-transform: translate(1px, -1px) rotate(1deg);\n    transform: translate(1px, -1px) rotate(1deg);\n  }\n  50% {\n    -webkit-transform: translate(-1px, 2px) rotate(-1deg);\n    transform: translate(-1px, 2px) rotate(-1deg);\n  }\n  60% {\n    -webkit-transform: translate(-3px, 1px) rotate(0deg);\n    transform: translate(-3px, 1px) rotate(0deg);\n  }\n  70% {\n    -webkit-transform: translate(2px, 1px) rotate(-2deg);\n    transform: translate(2px, 1px) rotate(-2deg);\n  }\n  80% {\n    -webkit-transform: translate(-1px, -1px) rotate(4deg);\n    transform: translate(-1px, -1px) rotate(4deg);\n  }\n  90% {\n    -webkit-transform: translate(2px, 2px) rotate(0deg);\n    transform: translate(2px, 2px) rotate(0deg);\n  }\n  100% {\n    -webkit-transform: translate(1px, -2px) rotate(-1deg);\n    transform: translate(1px, -2px) rotate(-1deg);\n  }\n}\n\n@-webkit-keyframes wobble {\n  0% {\n    -webkit-transform: none;\n    transform: none;\n  }\n  15% {\n    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\n    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\n  }\n  30% {\n    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\n    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\n  }\n  45% {\n    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\n    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\n  }\n  60% {\n    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\n    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\n  }\n  75% {\n    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\n    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\n  }\n  100% {\n    -webkit-transform: none;\n    transform: none;\n  }\n}\n\n@keyframes wobble {\n  0% {\n    -webkit-transform: none;\n    transform: none;\n  }\n  15% {\n    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\n    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\n  }\n  30% {\n    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\n    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\n  }\n  45% {\n    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\n    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\n  }\n  60% {\n    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\n    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\n  }\n  75% {\n    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\n    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\n  }\n  100% {\n    -webkit-transform: none;\n    transform: none;\n  }\n}\n\n.small-box {\n  border-radius: 0.25rem;\n  box-shadow: 0 0 1px rgba(0, 0, 0, 0.125), 0 1px 3px rgba(0, 0, 0, 0.2);\n  display: block;\n  margin-bottom: 20px;\n  position: relative;\n}\n\n.small-box > .inner {\n  padding: 10px;\n}\n\n.small-box > .small-box-footer {\n  background-color: rgba(0, 0, 0, 0.1);\n  color: rgba(255, 255, 255, 0.8);\n  display: block;\n  padding: 3px 0;\n  position: relative;\n  text-align: center;\n  text-decoration: none;\n  z-index: 10;\n}\n\n.small-box > .small-box-footer:hover {\n  background-color: rgba(0, 0, 0, 0.15);\n  color: #fff;\n}\n\n.small-box h3 {\n  font-size: 2.2rem;\n  font-weight: 700;\n  margin: 0 0 10px;\n  padding: 0;\n  white-space: nowrap;\n}\n\n@media (min-width: 992px) {\n  .col-xl-2 .small-box h3,\n  .col-lg-2 .small-box h3,\n  .col-md-2 .small-box h3 {\n    font-size: 1.6rem;\n  }\n  .col-xl-3 .small-box h3,\n  .col-lg-3 .small-box h3,\n  .col-md-3 .small-box h3 {\n    font-size: 1.6rem;\n  }\n}\n\n@media (min-width: 1200px) {\n  .col-xl-2 .small-box h3,\n  .col-lg-2 .small-box h3,\n  .col-md-2 .small-box h3 {\n    font-size: 2.2rem;\n  }\n  .col-xl-3 .small-box h3,\n  .col-lg-3 .small-box h3,\n  .col-md-3 .small-box h3 {\n    font-size: 2.2rem;\n  }\n}\n\n.small-box p {\n  font-size: 1rem;\n}\n\n.small-box p > small {\n  color: #f8f9fa;\n  display: block;\n  font-size: .9rem;\n  margin-top: 5px;\n}\n\n.small-box h3,\n.small-box p {\n  z-index: 5;\n}\n\n.small-box .icon {\n  color: rgba(0, 0, 0, 0.15);\n  z-index: 0;\n}\n\n.small-box .icon > i {\n  font-size: 90px;\n  position: absolute;\n  right: 15px;\n  top: 15px;\n  transition: -webkit-transform 0.3s linear;\n  transition: transform 0.3s linear;\n  transition: transform 0.3s linear, -webkit-transform 0.3s linear;\n}\n\n.small-box .icon > i.fa, .small-box .icon > i.fas, .small-box .icon > i.far, .small-box .icon > i.fab, .small-box .icon > i.fal, .small-box .icon > i.fad, .small-box .icon > i.ion {\n  font-size: 70px;\n  top: 20px;\n}\n\n.small-box .icon svg {\n  font-size: 70px;\n  position: absolute;\n  right: 15px;\n  top: 15px;\n  transition: -webkit-transform 0.3s linear;\n  transition: transform 0.3s linear;\n  transition: transform 0.3s linear, -webkit-transform 0.3s linear;\n}\n\n.small-box:hover {\n  text-decoration: none;\n}\n\n.small-box:hover .icon > i, .small-box:hover .icon > i.fa, .small-box:hover .icon > i.fas, .small-box:hover .icon > i.far, .small-box:hover .icon > i.fab, .small-box:hover .icon > i.fal, .small-box:hover .icon > i.fad, .small-box:hover .icon > i.ion {\n  -webkit-transform: scale(1.1);\n  transform: scale(1.1);\n}\n\n.small-box:hover .icon > svg {\n  -webkit-transform: scale(1.1);\n  transform: scale(1.1);\n}\n\n@media (max-width: 767.98px) {\n  .small-box {\n    text-align: center;\n  }\n  .small-box .icon {\n    display: none;\n  }\n  .small-box p {\n    font-size: 12px;\n  }\n}\n\n.info-box {\n  box-shadow: 0 0 1px rgba(0, 0, 0, 0.125), 0 1px 3px rgba(0, 0, 0, 0.2);\n  border-radius: 0.25rem;\n  background-color: #fff;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  margin-bottom: 1rem;\n  min-height: 80px;\n  padding: .5rem;\n  position: relative;\n  width: 100%;\n}\n\n.info-box .progress {\n  background-color: rgba(0, 0, 0, 0.125);\n  height: 2px;\n  margin: 5px 0;\n}\n\n.info-box .progress .progress-bar {\n  background-color: #fff;\n}\n\n.info-box .info-box-icon {\n  border-radius: 0.25rem;\n  -webkit-align-items: center;\n  -ms-flex-align: center;\n  align-items: center;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  font-size: 1.875rem;\n  -webkit-justify-content: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  text-align: center;\n  width: 70px;\n}\n\n.info-box .info-box-icon > img {\n  max-width: 100%;\n}\n\n.info-box .info-box-content {\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-flex-direction: column;\n  -ms-flex-direction: column;\n  flex-direction: column;\n  -webkit-justify-content: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  line-height: 1.8;\n  -webkit-flex: 1;\n  -ms-flex: 1;\n  flex: 1;\n  padding: 0 10px;\n}\n\n.info-box .info-box-number {\n  display: block;\n  margin-top: .25rem;\n  font-weight: 700;\n}\n\n.info-box .progress-description,\n.info-box .info-box-text {\n  display: block;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.info-box .info-box .bg-primary,\n.info-box .info-box .bg-gradient-primary {\n  color: #fff;\n}\n\n.info-box .info-box .bg-primary .progress-bar,\n.info-box .info-box .bg-gradient-primary .progress-bar {\n  background-color: #fff;\n}\n\n.info-box .info-box .bg-secondary,\n.info-box .info-box .bg-gradient-secondary {\n  color: #fff;\n}\n\n.info-box .info-box .bg-secondary .progress-bar,\n.info-box .info-box .bg-gradient-secondary .progress-bar {\n  background-color: #fff;\n}\n\n.info-box .info-box .bg-success,\n.info-box .info-box .bg-gradient-success {\n  color: #fff;\n}\n\n.info-box .info-box .bg-success .progress-bar,\n.info-box .info-box .bg-gradient-success .progress-bar {\n  background-color: #fff;\n}\n\n.info-box .info-box .bg-info,\n.info-box .info-box .bg-gradient-info {\n  color: #fff;\n}\n\n.info-box .info-box .bg-info .progress-bar,\n.info-box .info-box .bg-gradient-info .progress-bar {\n  background-color: #fff;\n}\n\n.info-box .info-box .bg-warning,\n.info-box .info-box .bg-gradient-warning {\n  color: #1f2d3d;\n}\n\n.info-box .info-box .bg-warning .progress-bar,\n.info-box .info-box .bg-gradient-warning .progress-bar {\n  background-color: #1f2d3d;\n}\n\n.info-box .info-box .bg-danger,\n.info-box .info-box .bg-gradient-danger {\n  color: #fff;\n}\n\n.info-box .info-box .bg-danger .progress-bar,\n.info-box .info-box .bg-gradient-danger .progress-bar {\n  background-color: #fff;\n}\n\n.info-box .info-box .bg-light,\n.info-box .info-box .bg-gradient-light {\n  color: #1f2d3d;\n}\n\n.info-box .info-box .bg-light .progress-bar,\n.info-box .info-box .bg-gradient-light .progress-bar {\n  background-color: #1f2d3d;\n}\n\n.info-box .info-box .bg-dark,\n.info-box .info-box .bg-gradient-dark {\n  color: #fff;\n}\n\n.info-box .info-box .bg-dark .progress-bar,\n.info-box .info-box .bg-gradient-dark .progress-bar {\n  background-color: #fff;\n}\n\n.info-box .info-box-more {\n  display: block;\n}\n\n.info-box .progress-description {\n  margin: 0;\n}\n\n@media (min-width: 768px) {\n  .col-xl-2 .info-box .progress-description,\n  .col-lg-2 .info-box .progress-description,\n  .col-md-2 .info-box .progress-description {\n    display: none;\n  }\n  .col-xl-3 .info-box .progress-description,\n  .col-lg-3 .info-box .progress-description,\n  .col-md-3 .info-box .progress-description {\n    display: none;\n  }\n}\n\n@media (min-width: 992px) {\n  .col-xl-2 .info-box .progress-description,\n  .col-lg-2 .info-box .progress-description,\n  .col-md-2 .info-box .progress-description {\n    font-size: 0.75rem;\n    display: block;\n  }\n  .col-xl-3 .info-box .progress-description,\n  .col-lg-3 .info-box .progress-description,\n  .col-md-3 .info-box .progress-description {\n    font-size: 0.75rem;\n    display: block;\n  }\n}\n\n@media (min-width: 1200px) {\n  .col-xl-2 .info-box .progress-description,\n  .col-lg-2 .info-box .progress-description,\n  .col-md-2 .info-box .progress-description {\n    font-size: 1rem;\n    display: block;\n  }\n  .col-xl-3 .info-box .progress-description,\n  .col-lg-3 .info-box .progress-description,\n  .col-md-3 .info-box .progress-description {\n    font-size: 1rem;\n    display: block;\n  }\n}\n\n.dark-mode .info-box {\n  background-color: #343a40;\n  color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-primary,\n.dark-mode .info-box .info-box .bg-gradient-primary {\n  color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-primary .progress-bar,\n.dark-mode .info-box .info-box .bg-gradient-primary .progress-bar {\n  background-color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-secondary,\n.dark-mode .info-box .info-box .bg-gradient-secondary {\n  color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-secondary .progress-bar,\n.dark-mode .info-box .info-box .bg-gradient-secondary .progress-bar {\n  background-color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-success,\n.dark-mode .info-box .info-box .bg-gradient-success {\n  color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-success .progress-bar,\n.dark-mode .info-box .info-box .bg-gradient-success .progress-bar {\n  background-color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-info,\n.dark-mode .info-box .info-box .bg-gradient-info {\n  color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-info .progress-bar,\n.dark-mode .info-box .info-box .bg-gradient-info .progress-bar {\n  background-color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-warning,\n.dark-mode .info-box .info-box .bg-gradient-warning {\n  color: #1f2d3d;\n}\n\n.dark-mode .info-box .info-box .bg-warning .progress-bar,\n.dark-mode .info-box .info-box .bg-gradient-warning .progress-bar {\n  background-color: #1f2d3d;\n}\n\n.dark-mode .info-box .info-box .bg-danger,\n.dark-mode .info-box .info-box .bg-gradient-danger {\n  color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-danger .progress-bar,\n.dark-mode .info-box .info-box .bg-gradient-danger .progress-bar {\n  background-color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-light,\n.dark-mode .info-box .info-box .bg-gradient-light {\n  color: #1f2d3d;\n}\n\n.dark-mode .info-box .info-box .bg-light .progress-bar,\n.dark-mode .info-box .info-box .bg-gradient-light .progress-bar {\n  background-color: #1f2d3d;\n}\n\n.dark-mode .info-box .info-box .bg-dark,\n.dark-mode .info-box .info-box .bg-gradient-dark {\n  color: #fff;\n}\n\n.dark-mode .info-box .info-box .bg-dark .progress-bar,\n.dark-mode .info-box .info-box .bg-gradient-dark .progress-bar {\n  background-color: #fff;\n}\n\n.timeline {\n  margin: 0 0 45px;\n  padding: 0;\n  position: relative;\n}\n\n.timeline::before {\n  border-radius: 0.25rem;\n  background-color: #dee2e6;\n  bottom: 0;\n  content: \"\";\n  left: 31px;\n  margin: 0;\n  position: absolute;\n  top: 0;\n  width: 4px;\n}\n\n.timeline > div {\n  margin-bottom: 15px;\n  margin-right: 10px;\n  position: relative;\n}\n\n.timeline > div::before, .timeline > div::after {\n  content: \"\";\n  display: table;\n}\n\n.timeline > div > .timeline-item {\n  box-shadow: 0 0 1px rgba(0, 0, 0, 0.125), 0 1px 3px rgba(0, 0, 0, 0.2);\n  border-radius: 0.25rem;\n  background-color: #fff;\n  color: #495057;\n  margin-left: 60px;\n  margin-right: 15px;\n  margin-top: 0;\n  padding: 0;\n  position: relative;\n}\n\n.timeline > div > .timeline-item > .time {\n  color: #999;\n  float: right;\n  font-size: 12px;\n  padding: 10px;\n}\n\n.timeline > div > .timeline-item > .timeline-header {\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125);\n  color: #495057;\n  font-size: 16px;\n  line-height: 1.1;\n  margin: 0;\n  padding: 10px;\n}\n\n.timeline > div > .timeline-item > .timeline-header > a {\n  font-weight: 600;\n}\n\n.timeline > div > .timeline-item > .timeline-body,\n.timeline > div > .timeline-item > .timeline-footer {\n  padding: 10px;\n}\n\n.timeline > div > .timeline-item > .timeline-body > img {\n  margin: 10px;\n}\n\n.timeline > div > .timeline-item > .timeline-body > dl,\n.timeline > div > .timeline-item > .timeline-body ol,\n.timeline > div > .timeline-item > .timeline-body ul {\n  margin: 0;\n}\n\n.timeline > div > .timeline-item > .timeline-footer > a {\n  color: #fff;\n}\n\n.timeline > div > .fa,\n.timeline > div > .fas,\n.timeline > div > .far,\n.timeline > div > .fab,\n.timeline > div > .fal,\n.timeline > div > .fad,\n.timeline > div > .svg-inline--fa,\n.timeline > div > .ion {\n  background-color: #adb5bd;\n  border-radius: 50%;\n  font-size: 16px;\n  height: 30px;\n  left: 18px;\n  line-height: 30px;\n  position: absolute;\n  text-align: center;\n  top: 0;\n  width: 30px;\n}\n\n.timeline > div > .svg-inline--fa {\n  padding: 7px;\n}\n\n.timeline > .time-label > span {\n  border-radius: 4px;\n  background-color: #fff;\n  display: inline-block;\n  font-weight: 600;\n  padding: 5px;\n}\n\n.timeline-inverse > div > .timeline-item {\n  box-shadow: none;\n  background-color: #f8f9fa;\n  border: 1px solid #dee2e6;\n}\n\n.timeline-inverse > div > .timeline-item > .timeline-header {\n  border-bottom-color: #dee2e6;\n}\n\n.dark-mode .timeline::before {\n  background-color: #6c757d;\n}\n\n.dark-mode .timeline > div > .timeline-item {\n  background-color: #343a40;\n  color: #fff;\n  border-color: #6c757d;\n}\n\n.dark-mode .timeline > div > .timeline-item > .timeline-header {\n  color: #ced4da;\n  border-color: #6c757d;\n}\n\n.dark-mode .timeline > div > .timeline-item > .time {\n  color: #ced4da;\n}\n\n.products-list {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n.products-list > .item {\n  border-radius: 0.25rem;\n  background-color: #fff;\n  padding: 10px 0;\n}\n\n.products-list > .item::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.products-list .product-img {\n  float: left;\n}\n\n.products-list .product-img img {\n  height: 50px;\n  width: 50px;\n}\n\n.products-list .product-info {\n  margin-left: 60px;\n}\n\n.products-list .product-title {\n  font-weight: 600;\n}\n\n.products-list .product-description {\n  color: #6c757d;\n  display: block;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.product-list-in-card > .item {\n  border-radius: 0;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125);\n}\n\n.product-list-in-card > .item:last-of-type {\n  border-bottom-width: 0;\n}\n\n.dark-mode .products-list > .item {\n  background-color: #343a40;\n  color: #fff;\n  border-bottom-color: #6c757d;\n}\n\n.dark-mode .product-description {\n  color: #ced4da;\n}\n\n.direct-chat .card-body {\n  overflow-x: hidden;\n  padding: 0;\n  position: relative;\n}\n\n.direct-chat.chat-pane-open .direct-chat-contacts {\n  -webkit-transform: translate(0, 0);\n  transform: translate(0, 0);\n}\n\n.direct-chat.timestamp-light .direct-chat-timestamp {\n  color: #30465f;\n}\n\n.direct-chat.timestamp-dark .direct-chat-timestamp {\n  color: #cccccc;\n}\n\n.direct-chat-messages {\n  -webkit-transform: translate(0, 0);\n  transform: translate(0, 0);\n  height: 250px;\n  overflow: auto;\n  padding: 10px;\n}\n\n.direct-chat-msg,\n.direct-chat-text {\n  display: block;\n}\n\n.direct-chat-msg {\n  margin-bottom: 10px;\n}\n\n.direct-chat-msg::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.direct-chat-messages,\n.direct-chat-contacts {\n  transition: -webkit-transform .5s ease-in-out;\n  transition: transform .5s ease-in-out;\n  transition: transform .5s ease-in-out, -webkit-transform .5s ease-in-out;\n}\n\n.direct-chat-text {\n  border-radius: 0.3rem;\n  background-color: #d2d6de;\n  border: 1px solid #d2d6de;\n  color: #444;\n  margin: 5px 0 0 50px;\n  padding: 5px 10px;\n  position: relative;\n}\n\n.direct-chat-text::after, .direct-chat-text::before {\n  border: solid transparent;\n  border-right-color: #d2d6de;\n  content: \" \";\n  height: 0;\n  pointer-events: none;\n  position: absolute;\n  right: 100%;\n  top: 15px;\n  width: 0;\n}\n\n.direct-chat-text::after {\n  border-width: 5px;\n  margin-top: -5px;\n}\n\n.direct-chat-text::before {\n  border-width: 6px;\n  margin-top: -6px;\n}\n\n.right .direct-chat-text {\n  margin-left: 0;\n  margin-right: 50px;\n}\n\n.right .direct-chat-text::after, .right .direct-chat-text::before {\n  border-left-color: #d2d6de;\n  border-right-color: transparent;\n  left: 100%;\n  right: auto;\n}\n\n.direct-chat-img {\n  border-radius: 50%;\n  float: left;\n  height: 40px;\n  width: 40px;\n}\n\n.right .direct-chat-img {\n  float: right;\n}\n\n.direct-chat-infos {\n  display: block;\n  font-size: 0.875rem;\n  margin-bottom: 2px;\n}\n\n.direct-chat-name {\n  font-weight: 600;\n}\n\n.direct-chat-timestamp {\n  color: #697582;\n}\n\n.direct-chat-contacts-open .direct-chat-contacts {\n  -webkit-transform: translate(0, 0);\n  transform: translate(0, 0);\n}\n\n.direct-chat-contacts {\n  -webkit-transform: translate(101%, 0);\n  transform: translate(101%, 0);\n  background-color: #343a40;\n  bottom: 0;\n  color: #fff;\n  height: 250px;\n  overflow: auto;\n  position: absolute;\n  top: 0;\n  width: 100%;\n}\n\n.direct-chat-contacts-light {\n  background-color: #f8f9fa;\n}\n\n.direct-chat-contacts-light .contacts-list-name {\n  color: #495057;\n}\n\n.direct-chat-contacts-light .contacts-list-date {\n  color: #6c757d;\n}\n\n.direct-chat-contacts-light .contacts-list-msg {\n  color: #545b62;\n}\n\n.contacts-list {\n  padding-left: 0;\n  list-style: none;\n}\n\n.contacts-list > li {\n  border-bottom: 1px solid rgba(0, 0, 0, 0.2);\n  margin: 0;\n  padding: 10px;\n}\n\n.contacts-list > li::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.contacts-list > li:last-of-type {\n  border-bottom: 0;\n}\n\n.contacts-list-img {\n  border-radius: 50%;\n  float: left;\n  width: 40px;\n}\n\n.contacts-list-info {\n  color: #fff;\n  margin-left: 45px;\n}\n\n.contacts-list-name,\n.contacts-list-status {\n  display: block;\n}\n\n.contacts-list-name {\n  font-weight: 600;\n}\n\n.contacts-list-status {\n  font-size: 0.875rem;\n}\n\n.contacts-list-date {\n  color: #ced4da;\n  font-weight: 400;\n}\n\n.contacts-list-msg {\n  color: #b1bbc4;\n}\n\n.direct-chat-primary .right > .direct-chat-text {\n  background-color: #007bff;\n  border-color: #007bff;\n  color: #fff;\n}\n\n.direct-chat-primary .right > .direct-chat-text::after, .direct-chat-primary .right > .direct-chat-text::before {\n  border-left-color: #007bff;\n}\n\n.direct-chat-secondary .right > .direct-chat-text {\n  background-color: #6c757d;\n  border-color: #6c757d;\n  color: #fff;\n}\n\n.direct-chat-secondary .right > .direct-chat-text::after, .direct-chat-secondary .right > .direct-chat-text::before {\n  border-left-color: #6c757d;\n}\n\n.direct-chat-success .right > .direct-chat-text {\n  background-color: #28a745;\n  border-color: #28a745;\n  color: #fff;\n}\n\n.direct-chat-success .right > .direct-chat-text::after, .direct-chat-success .right > .direct-chat-text::before {\n  border-left-color: #28a745;\n}\n\n.direct-chat-info .right > .direct-chat-text {\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n  color: #fff;\n}\n\n.direct-chat-info .right > .direct-chat-text::after, .direct-chat-info .right > .direct-chat-text::before {\n  border-left-color: #17a2b8;\n}\n\n.direct-chat-warning .right > .direct-chat-text {\n  background-color: #ffc107;\n  border-color: #ffc107;\n  color: #1f2d3d;\n}\n\n.direct-chat-warning .right > .direct-chat-text::after, .direct-chat-warning .right > .direct-chat-text::before {\n  border-left-color: #ffc107;\n}\n\n.direct-chat-danger .right > .direct-chat-text {\n  background-color: #dc3545;\n  border-color: #dc3545;\n  color: #fff;\n}\n\n.direct-chat-danger .right > .direct-chat-text::after, .direct-chat-danger .right > .direct-chat-text::before {\n  border-left-color: #dc3545;\n}\n\n.direct-chat-light .right > .direct-chat-text {\n  background-color: #f8f9fa;\n  border-color: #f8f9fa;\n  color: #1f2d3d;\n}\n\n.direct-chat-light .right > .direct-chat-text::after, .direct-chat-light .right > .direct-chat-text::before {\n  border-left-color: #f8f9fa;\n}\n\n.direct-chat-dark .right > .direct-chat-text {\n  background-color: #343a40;\n  border-color: #343a40;\n  color: #fff;\n}\n\n.direct-chat-dark .right > .direct-chat-text::after, .direct-chat-dark .right > .direct-chat-text::before {\n  border-left-color: #343a40;\n}\n\n.direct-chat-lightblue .right > .direct-chat-text {\n  background-color: #3c8dbc;\n  border-color: #3c8dbc;\n  color: #fff;\n}\n\n.direct-chat-lightblue .right > .direct-chat-text::after, .direct-chat-lightblue .right > .direct-chat-text::before {\n  border-left-color: #3c8dbc;\n}\n\n.direct-chat-navy .right > .direct-chat-text {\n  background-color: #001f3f;\n  border-color: #001f3f;\n  color: #fff;\n}\n\n.direct-chat-navy .right > .direct-chat-text::after, .direct-chat-navy .right > .direct-chat-text::before {\n  border-left-color: #001f3f;\n}\n\n.direct-chat-olive .right > .direct-chat-text {\n  background-color: #3d9970;\n  border-color: #3d9970;\n  color: #fff;\n}\n\n.direct-chat-olive .right > .direct-chat-text::after, .direct-chat-olive .right > .direct-chat-text::before {\n  border-left-color: #3d9970;\n}\n\n.direct-chat-lime .right > .direct-chat-text {\n  background-color: #01ff70;\n  border-color: #01ff70;\n  color: #1f2d3d;\n}\n\n.direct-chat-lime .right > .direct-chat-text::after, .direct-chat-lime .right > .direct-chat-text::before {\n  border-left-color: #01ff70;\n}\n\n.direct-chat-fuchsia .right > .direct-chat-text {\n  background-color: #f012be;\n  border-color: #f012be;\n  color: #fff;\n}\n\n.direct-chat-fuchsia .right > .direct-chat-text::after, .direct-chat-fuchsia .right > .direct-chat-text::before {\n  border-left-color: #f012be;\n}\n\n.direct-chat-maroon .right > .direct-chat-text {\n  background-color: #d81b60;\n  border-color: #d81b60;\n  color: #fff;\n}\n\n.direct-chat-maroon .right > .direct-chat-text::after, .direct-chat-maroon .right > .direct-chat-text::before {\n  border-left-color: #d81b60;\n}\n\n.direct-chat-blue .right > .direct-chat-text {\n  background-color: #007bff;\n  border-color: #007bff;\n  color: #fff;\n}\n\n.direct-chat-blue .right > .direct-chat-text::after, .direct-chat-blue .right > .direct-chat-text::before {\n  border-left-color: #007bff;\n}\n\n.direct-chat-indigo .right > .direct-chat-text {\n  background-color: #6610f2;\n  border-color: #6610f2;\n  color: #fff;\n}\n\n.direct-chat-indigo .right > .direct-chat-text::after, .direct-chat-indigo .right > .direct-chat-text::before {\n  border-left-color: #6610f2;\n}\n\n.direct-chat-purple .right > .direct-chat-text {\n  background-color: #6f42c1;\n  border-color: #6f42c1;\n  color: #fff;\n}\n\n.direct-chat-purple .right > .direct-chat-text::after, .direct-chat-purple .right > .direct-chat-text::before {\n  border-left-color: #6f42c1;\n}\n\n.direct-chat-pink .right > .direct-chat-text {\n  background-color: #e83e8c;\n  border-color: #e83e8c;\n  color: #fff;\n}\n\n.direct-chat-pink .right > .direct-chat-text::after, .direct-chat-pink .right > .direct-chat-text::before {\n  border-left-color: #e83e8c;\n}\n\n.direct-chat-red .right > .direct-chat-text {\n  background-color: #dc3545;\n  border-color: #dc3545;\n  color: #fff;\n}\n\n.direct-chat-red .right > .direct-chat-text::after, .direct-chat-red .right > .direct-chat-text::before {\n  border-left-color: #dc3545;\n}\n\n.direct-chat-orange .right > .direct-chat-text {\n  background-color: #fd7e14;\n  border-color: #fd7e14;\n  color: #1f2d3d;\n}\n\n.direct-chat-orange .right > .direct-chat-text::after, .direct-chat-orange .right > .direct-chat-text::before {\n  border-left-color: #fd7e14;\n}\n\n.direct-chat-yellow .right > .direct-chat-text {\n  background-color: #ffc107;\n  border-color: #ffc107;\n  color: #1f2d3d;\n}\n\n.direct-chat-yellow .right > .direct-chat-text::after, .direct-chat-yellow .right > .direct-chat-text::before {\n  border-left-color: #ffc107;\n}\n\n.direct-chat-green .right > .direct-chat-text {\n  background-color: #28a745;\n  border-color: #28a745;\n  color: #fff;\n}\n\n.direct-chat-green .right > .direct-chat-text::after, .direct-chat-green .right > .direct-chat-text::before {\n  border-left-color: #28a745;\n}\n\n.direct-chat-teal .right > .direct-chat-text {\n  background-color: #20c997;\n  border-color: #20c997;\n  color: #fff;\n}\n\n.direct-chat-teal .right > .direct-chat-text::after, .direct-chat-teal .right > .direct-chat-text::before {\n  border-left-color: #20c997;\n}\n\n.direct-chat-cyan .right > .direct-chat-text {\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n  color: #fff;\n}\n\n.direct-chat-cyan .right > .direct-chat-text::after, .direct-chat-cyan .right > .direct-chat-text::before {\n  border-left-color: #17a2b8;\n}\n\n.direct-chat-white .right > .direct-chat-text {\n  background-color: #fff;\n  border-color: #fff;\n  color: #1f2d3d;\n}\n\n.direct-chat-white .right > .direct-chat-text::after, .direct-chat-white .right > .direct-chat-text::before {\n  border-left-color: #fff;\n}\n\n.direct-chat-gray .right > .direct-chat-text {\n  background-color: #6c757d;\n  border-color: #6c757d;\n  color: #fff;\n}\n\n.direct-chat-gray .right > .direct-chat-text::after, .direct-chat-gray .right > .direct-chat-text::before {\n  border-left-color: #6c757d;\n}\n\n.direct-chat-gray-dark .right > .direct-chat-text {\n  background-color: #343a40;\n  border-color: #343a40;\n  color: #fff;\n}\n\n.direct-chat-gray-dark .right > .direct-chat-text::after, .direct-chat-gray-dark .right > .direct-chat-text::before {\n  border-left-color: #343a40;\n}\n\n.dark-mode .direct-chat-text {\n  background-color: #454d55;\n  border-color: #4b545c;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-text::after, .dark-mode .direct-chat-text::before {\n  border-right-color: #4b545c;\n}\n\n.dark-mode .direct-chat-timestamp {\n  color: #adb5bd;\n}\n\n.dark-mode .right > .direct-chat-text::after, .dark-mode .right > .direct-chat-text::before {\n  border-right-color: transparent;\n}\n\n.dark-mode .direct-chat-primary .right > .direct-chat-text {\n  background-color: #3f6791;\n  border-color: #3f6791;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-primary .right > .direct-chat-text::after, .dark-mode .direct-chat-primary .right > .direct-chat-text::before {\n  border-left-color: #3f6791;\n}\n\n.dark-mode .direct-chat-secondary .right > .direct-chat-text {\n  background-color: #6c757d;\n  border-color: #6c757d;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-secondary .right > .direct-chat-text::after, .dark-mode .direct-chat-secondary .right > .direct-chat-text::before {\n  border-left-color: #6c757d;\n}\n\n.dark-mode .direct-chat-success .right > .direct-chat-text {\n  background-color: #00bc8c;\n  border-color: #00bc8c;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-success .right > .direct-chat-text::after, .dark-mode .direct-chat-success .right > .direct-chat-text::before {\n  border-left-color: #00bc8c;\n}\n\n.dark-mode .direct-chat-info .right > .direct-chat-text {\n  background-color: #3498db;\n  border-color: #3498db;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-info .right > .direct-chat-text::after, .dark-mode .direct-chat-info .right > .direct-chat-text::before {\n  border-left-color: #3498db;\n}\n\n.dark-mode .direct-chat-warning .right > .direct-chat-text {\n  background-color: #f39c12;\n  border-color: #f39c12;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-warning .right > .direct-chat-text::after, .dark-mode .direct-chat-warning .right > .direct-chat-text::before {\n  border-left-color: #f39c12;\n}\n\n.dark-mode .direct-chat-danger .right > .direct-chat-text {\n  background-color: #e74c3c;\n  border-color: #e74c3c;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-danger .right > .direct-chat-text::after, .dark-mode .direct-chat-danger .right > .direct-chat-text::before {\n  border-left-color: #e74c3c;\n}\n\n.dark-mode .direct-chat-light .right > .direct-chat-text {\n  background-color: #f8f9fa;\n  border-color: #f8f9fa;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-light .right > .direct-chat-text::after, .dark-mode .direct-chat-light .right > .direct-chat-text::before {\n  border-left-color: #f8f9fa;\n}\n\n.dark-mode .direct-chat-dark .right > .direct-chat-text {\n  background-color: #343a40;\n  border-color: #343a40;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-dark .right > .direct-chat-text::after, .dark-mode .direct-chat-dark .right > .direct-chat-text::before {\n  border-left-color: #343a40;\n}\n\n.dark-mode .direct-chat-lightblue .right > .direct-chat-text {\n  background-color: #86bad8;\n  border-color: #86bad8;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-lightblue .right > .direct-chat-text::after, .dark-mode .direct-chat-lightblue .right > .direct-chat-text::before {\n  border-left-color: #86bad8;\n}\n\n.dark-mode .direct-chat-navy .right > .direct-chat-text {\n  background-color: #002c59;\n  border-color: #002c59;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-navy .right > .direct-chat-text::after, .dark-mode .direct-chat-navy .right > .direct-chat-text::before {\n  border-left-color: #002c59;\n}\n\n.dark-mode .direct-chat-olive .right > .direct-chat-text {\n  background-color: #74c8a3;\n  border-color: #74c8a3;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-olive .right > .direct-chat-text::after, .dark-mode .direct-chat-olive .right > .direct-chat-text::before {\n  border-left-color: #74c8a3;\n}\n\n.dark-mode .direct-chat-lime .right > .direct-chat-text {\n  background-color: #67ffa9;\n  border-color: #67ffa9;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-lime .right > .direct-chat-text::after, .dark-mode .direct-chat-lime .right > .direct-chat-text::before {\n  border-left-color: #67ffa9;\n}\n\n.dark-mode .direct-chat-fuchsia .right > .direct-chat-text {\n  background-color: #f672d8;\n  border-color: #f672d8;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-fuchsia .right > .direct-chat-text::after, .dark-mode .direct-chat-fuchsia .right > .direct-chat-text::before {\n  border-left-color: #f672d8;\n}\n\n.dark-mode .direct-chat-maroon .right > .direct-chat-text {\n  background-color: #ed6c9b;\n  border-color: #ed6c9b;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-maroon .right > .direct-chat-text::after, .dark-mode .direct-chat-maroon .right > .direct-chat-text::before {\n  border-left-color: #ed6c9b;\n}\n\n.dark-mode .direct-chat-blue .right > .direct-chat-text {\n  background-color: #3f6791;\n  border-color: #3f6791;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-blue .right > .direct-chat-text::after, .dark-mode .direct-chat-blue .right > .direct-chat-text::before {\n  border-left-color: #3f6791;\n}\n\n.dark-mode .direct-chat-indigo .right > .direct-chat-text {\n  background-color: #6610f2;\n  border-color: #6610f2;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-indigo .right > .direct-chat-text::after, .dark-mode .direct-chat-indigo .right > .direct-chat-text::before {\n  border-left-color: #6610f2;\n}\n\n.dark-mode .direct-chat-purple .right > .direct-chat-text {\n  background-color: #6f42c1;\n  border-color: #6f42c1;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-purple .right > .direct-chat-text::after, .dark-mode .direct-chat-purple .right > .direct-chat-text::before {\n  border-left-color: #6f42c1;\n}\n\n.dark-mode .direct-chat-pink .right > .direct-chat-text {\n  background-color: #e83e8c;\n  border-color: #e83e8c;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-pink .right > .direct-chat-text::after, .dark-mode .direct-chat-pink .right > .direct-chat-text::before {\n  border-left-color: #e83e8c;\n}\n\n.dark-mode .direct-chat-red .right > .direct-chat-text {\n  background-color: #e74c3c;\n  border-color: #e74c3c;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-red .right > .direct-chat-text::after, .dark-mode .direct-chat-red .right > .direct-chat-text::before {\n  border-left-color: #e74c3c;\n}\n\n.dark-mode .direct-chat-orange .right > .direct-chat-text {\n  background-color: #fd7e14;\n  border-color: #fd7e14;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-orange .right > .direct-chat-text::after, .dark-mode .direct-chat-orange .right > .direct-chat-text::before {\n  border-left-color: #fd7e14;\n}\n\n.dark-mode .direct-chat-yellow .right > .direct-chat-text {\n  background-color: #f39c12;\n  border-color: #f39c12;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-yellow .right > .direct-chat-text::after, .dark-mode .direct-chat-yellow .right > .direct-chat-text::before {\n  border-left-color: #f39c12;\n}\n\n.dark-mode .direct-chat-green .right > .direct-chat-text {\n  background-color: #00bc8c;\n  border-color: #00bc8c;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-green .right > .direct-chat-text::after, .dark-mode .direct-chat-green .right > .direct-chat-text::before {\n  border-left-color: #00bc8c;\n}\n\n.dark-mode .direct-chat-teal .right > .direct-chat-text {\n  background-color: #20c997;\n  border-color: #20c997;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-teal .right > .direct-chat-text::after, .dark-mode .direct-chat-teal .right > .direct-chat-text::before {\n  border-left-color: #20c997;\n}\n\n.dark-mode .direct-chat-cyan .right > .direct-chat-text {\n  background-color: #3498db;\n  border-color: #3498db;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-cyan .right > .direct-chat-text::after, .dark-mode .direct-chat-cyan .right > .direct-chat-text::before {\n  border-left-color: #3498db;\n}\n\n.dark-mode .direct-chat-white .right > .direct-chat-text {\n  background-color: #fff;\n  border-color: #fff;\n  color: #1f2d3d;\n}\n\n.dark-mode .direct-chat-white .right > .direct-chat-text::after, .dark-mode .direct-chat-white .right > .direct-chat-text::before {\n  border-left-color: #fff;\n}\n\n.dark-mode .direct-chat-gray .right > .direct-chat-text {\n  background-color: #6c757d;\n  border-color: #6c757d;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-gray .right > .direct-chat-text::after, .dark-mode .direct-chat-gray .right > .direct-chat-text::before {\n  border-left-color: #6c757d;\n}\n\n.dark-mode .direct-chat-gray-dark .right > .direct-chat-text {\n  background-color: #343a40;\n  border-color: #343a40;\n  color: #fff;\n}\n\n.dark-mode .direct-chat-gray-dark .right > .direct-chat-text::after, .dark-mode .direct-chat-gray-dark .right > .direct-chat-text::before {\n  border-left-color: #343a40;\n}\n\n.users-list {\n  padding-left: 0;\n  list-style: none;\n}\n\n.users-list > li {\n  float: left;\n  padding: 10px;\n  text-align: center;\n  width: 25%;\n}\n\n.users-list > li img {\n  border-radius: 50%;\n  height: auto;\n  max-width: 100%;\n}\n\n.users-list > li > a:hover,\n.users-list > li > a:hover .users-list-name {\n  color: #999;\n}\n\n.users-list-name,\n.users-list-date {\n  display: block;\n}\n\n.users-list-name {\n  color: #495057;\n  font-size: 0.875rem;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.users-list-date {\n  color: #748290;\n  font-size: 12px;\n}\n\n.dark-mode .users-list-name {\n  color: #ced4da;\n}\n\n.dark-mode .users-list-date {\n  color: #adb5bd;\n}\n\n.card-widget {\n  border: 0;\n  position: relative;\n}\n\n.widget-user .widget-user-header {\n  border-top-left-radius: 0.25rem;\n  border-top-right-radius: 0.25rem;\n  height: 135px;\n  padding: 1rem;\n  text-align: center;\n}\n\n.widget-user .widget-user-username {\n  font-size: 25px;\n  font-weight: 300;\n  margin-bottom: 0;\n  margin-top: 0;\n  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);\n}\n\n.widget-user .widget-user-desc {\n  margin-top: 0;\n}\n\n.widget-user .widget-user-image {\n  left: 50%;\n  margin-left: -45px;\n  position: absolute;\n  top: 80px;\n}\n\n.widget-user .widget-user-image > img {\n  border: 3px solid #fff;\n  height: auto;\n  width: 90px;\n}\n\n.widget-user .card-footer {\n  padding-top: 50px;\n}\n\n.widget-user-2 .widget-user-header {\n  border-top-left-radius: 0.25rem;\n  border-top-right-radius: 0.25rem;\n  padding: 1rem;\n}\n\n.widget-user-2 .widget-user-username {\n  font-size: 25px;\n  font-weight: 300;\n  margin-bottom: 5px;\n  margin-top: 5px;\n}\n\n.widget-user-2 .widget-user-desc {\n  margin-top: 0;\n}\n\n.widget-user-2 .widget-user-username,\n.widget-user-2 .widget-user-desc {\n  margin-left: 75px;\n}\n\n.widget-user-2 .widget-user-image > img {\n  float: left;\n  height: auto;\n  width: 65px;\n}\n/*# sourceMappingURL=adminlte.extra-components.css.map */", "//\n// Component: Info Box\n//\n\n.info-box {\n  @include box-shadow($card-shadow);\n  @include border-radius($border-radius);\n\n  background-color: $white;\n  display: flex;\n  margin-bottom: map-get($spacers, 3);\n  min-height: 80px;\n  padding: .5rem;\n  position: relative;\n  width: 100%;\n\n  .progress {\n    background-color: rgba($black, .125);\n    height: 2px;\n    margin: 5px 0;\n\n    .progress-bar {\n      background-color: $white;\n    }\n  }\n\n  .info-box-icon {\n    @if $enable-rounded {\n      border-radius: $border-radius;\n    }\n\n    align-items: center;\n    display: flex;\n    font-size: 1.875rem;\n    justify-content: center;\n    text-align: center;\n    width: 70px;\n\n    > img {\n      max-width: 100%;\n    }\n  }\n\n  .info-box-content {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    line-height: 1.8;\n    flex: 1;\n    padding: 0 10px;\n  }\n\n  .info-box-number {\n    display: block;\n    margin-top: .25rem;\n    font-weight: $font-weight-bold;\n  }\n\n  .progress-description,\n  .info-box-text {\n    display: block;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n\n  @each $name, $color in $theme-colors {\n    .info-box {\n      .bg-#{$name},\n      .bg-gradient-#{$name} {\n        color: color-yiq($color);\n\n        .progress-bar {\n          background-color: color-yiq($color);\n        }\n      }\n    }\n  }\n\n  .info-box-more {\n    display: block;\n  }\n\n  .progress-description {\n    margin: 0;\n\n  }\n\n  @include media-breakpoint-up(md) {\n    .col-xl-2 &,\n    .col-lg-2 &,\n    .col-md-2 & {\n      .progress-description {\n        display: none;\n      }\n    }\n\n    .col-xl-3 &,\n    .col-lg-3 &,\n    .col-md-3 & {\n      .progress-description {\n        display: none;\n      }\n    }\n  }\n\n  @include media-breakpoint-up(lg) {\n    .col-xl-2 &,\n    .col-lg-2 &,\n    .col-md-2 & {\n      .progress-description {\n        @include font-size(.75rem);\n        display: block;\n      }\n    }\n\n    .col-xl-3 &,\n    .col-lg-3 &,\n    .col-md-3 & {\n      .progress-description {\n        @include font-size(.75rem);\n        display: block;\n      }\n    }\n  }\n\n  @include media-breakpoint-up(xl) {\n    .col-xl-2 &,\n    .col-lg-2 &,\n    .col-md-2 & {\n      .progress-description {\n        @include font-size(1rem);\n        display: block;\n      }\n    }\n\n    .col-xl-3 &,\n    .col-lg-3 &,\n    .col-md-3 & {\n      .progress-description {\n        @include font-size(1rem);\n        display: block;\n      }\n    }\n  }\n}\n\n.dark-mode {\n  .info-box {\n    background-color: $dark;\n    color: $white;\n    @each $name, $color in $theme-colors-alt {\n      .info-box {\n        .bg-#{$name},\n        .bg-gradient-#{$name} {\n          color: color-yiq($color);\n\n          .progress-bar {\n            background-color: color-yiq($color);\n          }\n        }\n      }\n    }\n  }\n}\n", "//\n// Component: Timeline\n//\n\n.timeline {\n  margin: 0 0 45px;\n  padding: 0;\n  position: relative;\n  // The line\n  &::before {\n    @include border-radius($border-radius);\n    background-color: $gray-300;\n    bottom: 0;\n    content: \"\";\n    left: 31px;\n    margin: 0;\n    position: absolute;\n    top: 0;\n    width: 4px;\n  }\n  // Element\n  > div {\n    &::before,\n    &::after {\n      content: \"\";\n      display: table;\n    }\n\n    margin-bottom: 15px;\n    margin-right: 10px;\n    position: relative;\n    // The content\n    > .timeline-item {\n      @include box-shadow($card-shadow);\n      @include border-radius($border-radius);\n      background-color: $white;\n      color: $gray-700;\n      margin-left: 60px;\n      margin-right: 15px;\n      margin-top: 0;\n      padding: 0;\n      position: relative;\n      // The time and header\n      > .time {\n        color: #999;\n        float: right;\n        font-size: 12px;\n        padding: 10px;\n      }\n      // Header\n      > .timeline-header {\n        border-bottom: 1px solid $card-border-color;\n        color: $gray-700;\n        font-size: 16px;\n        line-height: 1.1;\n        margin: 0;\n        padding: 10px;\n        // Link in header\n        > a {\n          font-weight: 600;\n        }\n      }\n      // Item body and footer\n      > .timeline-body,\n      > .timeline-footer {\n        padding: 10px;\n      }\n\n      > .timeline-body {\n        > img {\n          margin: 10px;\n        }\n        > dl,\n        ol,\n        ul {\n          margin: 0;\n        }\n      }\n\n      > .timeline-footer {\n        > a {\n          color: $white;\n        }\n      }\n    }\n    // The icons at line\n    > .fa,\n    > .fas,\n    > .far,\n    > .fab,\n    > .fal,\n    > .fad,\n    > .svg-inline--fa,\n    > .ion {\n      background-color: $gray-500;\n      border-radius: 50%;\n      font-size: 16px;\n      height: 30px;\n      left: 18px;\n      line-height: 30px;\n      position: absolute;\n      text-align: center;\n      top: 0;\n      width: 30px;\n    }\n    > .svg-inline--fa {\n      padding: 7px;\n    }\n  }\n  // Time label\n  > .time-label {\n    > span {\n      @include border-radius(4px);\n      background-color: $white;\n      display: inline-block;\n      font-weight: 600;\n      padding: 5px;\n    }\n  }\n}\n\n.timeline-inverse {\n  > div {\n    > .timeline-item {\n      @include box-shadow(none);\n      background-color: $gray-100;\n      border: 1px solid $gray-300;\n\n      > .timeline-header {\n        border-bottom-color: $gray-300;\n      }\n    }\n  }\n}\n\n.dark-mode {\n  .timeline {\n    &::before {\n      background-color: $gray-600;\n    }\n    > div > .timeline-item {\n      background-color: $dark;\n      color: $white;\n      border-color: $gray-600;\n\n      > .timeline-header {\n        color: $gray-400;\n        border-color: $gray-600;\n      }\n      > .time {\n        color: $gray-400;\n      }\n    }\n  }\n}\n", "//\n// Component: Products\n//\n\n.products-list {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n\n  > .item {\n    @include clearfix ();\n\n    @if $enable-rounded {\n      @include border-radius($border-radius);\n    }\n\n    background-color: $white;\n    padding: 10px 0;\n\n  }\n\n  .product-img {\n    float: left;\n\n    img {\n      height: 50px;\n      width: 50px;\n    }\n  }\n\n  .product-info {\n    margin-left: 60px;\n  }\n\n  .product-title {\n    font-weight: 600;\n  }\n\n  .product-description {\n    color: $gray-600;\n    display: block;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n}\n\n.product-list-in-card > .item {\n  @include border-radius(0);\n  border-bottom: 1px solid $card-border-color;\n\n  &:last-of-type {\n    border-bottom-width: 0;\n  }\n}\n\n\n.dark-mode {\n  .products-list > .item {\n    background-color: $dark;\n    color: $white;\n    border-bottom-color: $gray-600;\n  }\n\n  .product-description {\n    color: $gray-400;\n  }\n}\n", "@mixin clearfix() {\n  &::after {\n    display: block;\n    clear: both;\n    content: \"\";\n  }\n}\n", "//\n// Component: Direct Chat\n//\n\n.direct-chat {\n  .card-body {\n    overflow-x: hidden;\n    padding: 0;\n    position: relative;\n  }\n\n  &.chat-pane-open {\n    .direct-chat-contacts {\n      @include translate(0, 0);\n    }\n  }\n\n\n  &.timestamp-light {\n    .direct-chat-timestamp {\n      color: lighten(color-yiq($yiq-text-light), 10%);\n    }\n  }\n\n  &.timestamp-dark {\n    .direct-chat-timestamp {\n      color: darken(color-yiq($yiq-text-dark), 20%);\n    }\n  }\n}\n\n.direct-chat-messages {\n  @include translate(0, 0);\n  height: 250px;\n  overflow: auto;\n  padding: 10px;\n}\n\n.direct-chat-msg,\n.direct-chat-text {\n  display: block;\n}\n\n.direct-chat-msg {\n  @include clearfix ();\n  margin-bottom: 10px;\n}\n\n.direct-chat-messages,\n.direct-chat-contacts {\n  transition: transform .5s ease-in-out;\n}\n\n.direct-chat-text {\n  @if $enable-rounded {\n    @include border-radius($border-radius-lg);\n  }\n\n  background-color: $direct-chat-default-msg-bg;\n  border: 1px solid $direct-chat-default-msg-border-color;\n  color: $direct-chat-default-font-color;\n  margin: 5px 0 0 50px;\n  padding: 5px 10px;\n  position: relative;\n\n  //Create the arrow\n  &::after,\n  &::before {\n    border: solid transparent;\n    border-right-color: $direct-chat-default-msg-border-color;\n    content: \" \";\n    height: 0;\n    pointer-events: none;\n    position: absolute;\n    right: 100%;\n    top: 15px;\n    width: 0;\n  }\n\n  &::after {\n    border-width: 5px;\n    margin-top: -5px;\n  }\n\n  &::before {\n    border-width: 6px;\n    margin-top: -6px;\n  }\n\n  .right & {\n    margin-left: 0;\n    margin-right: 50px;\n\n    &::after,\n    &::before {\n      border-left-color: $direct-chat-default-msg-border-color;\n      border-right-color: transparent;\n      left: 100%;\n      right: auto;\n    }\n  }\n}\n\n.direct-chat-img {\n  @include border-radius(50%);\n  float: left;\n  height: 40px;\n  width: 40px;\n\n  .right & {\n    float: right;\n  }\n}\n\n.direct-chat-infos {\n  display: block;\n  font-size: $font-size-sm;\n  margin-bottom: 2px;\n}\n\n.direct-chat-name {\n  font-weight: 600;\n}\n\n.direct-chat-timestamp {\n  color: darken($gray-500, 25%);\n}\n\n//Direct chat contacts pane\n.direct-chat-contacts-open {\n  .direct-chat-contacts {\n    @include translate(0, 0);\n  }\n}\n\n.direct-chat-contacts {\n  @include translate(101%, 0);\n  background-color: $dark;\n  bottom: 0;\n  color: $white;\n  height: 250px;\n  overflow: auto;\n  position: absolute;\n  top: 0;\n  width: 100%;\n}\n\n.direct-chat-contacts-light {\n  background-color: $light;\n\n  .contacts-list-name {\n    color: $gray-700;\n  }\n\n  .contacts-list-date {\n    color: $gray-600;\n  }\n\n  .contacts-list-msg {\n    color: darken($gray-600, 10%);\n  }\n}\n\n//Contacts list -- for displaying contacts in direct chat contacts pane\n.contacts-list {\n  @include list-unstyled ();\n\n  > li {\n    @include clearfix ();\n    border-bottom: 1px solid rgba($black, .2);\n    margin: 0;\n    padding: 10px;\n\n    &:last-of-type {\n      border-bottom: 0;\n    }\n  }\n}\n\n.contacts-list-img {\n  @include border-radius(50%);\n  float: left;\n  width: 40px;\n}\n\n.contacts-list-info {\n  color: $white;\n  margin-left: 45px;\n}\n\n.contacts-list-name,\n.contacts-list-status {\n  display: block;\n}\n\n.contacts-list-name {\n  font-weight: 600;\n}\n\n.contacts-list-status {\n  font-size: $font-size-sm;\n}\n\n.contacts-list-date {\n  color: $gray-400;\n  font-weight: 400;\n}\n\n.contacts-list-msg {\n  color: darken($gray-400, 10%);\n}\n\n// Color variants\n@each $name, $color in $theme-colors {\n  .direct-chat-#{$name} {\n    @include direct-chat-variant($color);\n  }\n}\n\n@each $name, $color in $colors {\n  .direct-chat-#{$name} {\n    @include direct-chat-variant($color);\n  }\n}\n\n.dark-mode {\n  .direct-chat-text {\n    background-color: lighten($dark, 7.5%);\n    border-color: lighten($dark, 10%);\n    color: $white;\n\n    &::after,\n    &::before {\n      border-right-color: lighten($dark, 10%);\n    }\n  }\n  .direct-chat-timestamp {\n    color: $gray-500;\n  }\n  .right > .direct-chat-text {\n    &::after,\n    &::before {\n      border-right-color: transparent;\n    }\n  }\n\n  // Color variants\n  @each $name, $color in $theme-colors-alt {\n    .direct-chat-#{$name} {\n      @include direct-chat-variant($color);\n    }\n  }\n\n  @each $name, $color in $colors-alt {\n    .direct-chat-#{$name} {\n      @include direct-chat-variant($color);\n    }\n  }\n}\n", "//\n// Mixins: Miscellaneous\n//\n\n// ETC\n@mixin translate($x, $y) {\n  transform: translate($x, $y);\n}\n\n// Different radius each side\n@mixin border-radius-sides($top-left, $top-right, $bottom-left, $bottom-right) {\n  border-radius: $top-left $top-right $bottom-left $bottom-right;\n}\n\n@mixin calc($property, $expression) {\n  #{$property}: calc(#{$expression});\n}\n\n@mixin rotate($value) {\n  transform: rotate($value);\n}\n\n@mixin animation($animation) {\n  animation: $animation;\n}\n\n// Gradient background\n@mixin gradient($color: #f5f5f5, $start: #eee, $stop: $white) {\n  background-color: $color;\n  background-image: gradient(linear, left bottom, left top, color-stop(0, $start), color-stop(1, $stop));\n}\n\n@mixin scrollbar-width-thin() {\n  scrollbar-width: thin;\n  scrollbar-color: #a9a9a9 transparent;\n}\n\n@mixin scrollbar-width-none() {\n  scrollbar-width: none;\n}\n\n//\n", "// Lists\n\n// Unstyled keeps list items block level, just removes default browser padding and list-style\n@mixin list-unstyled() {\n  padding-left: 0;\n  list-style: none;\n}\n", "//\n// Mixins: Direct Chat\n//\n\n// Direct Chat Variant\n@mixin direct-chat-variant($bg-color, $color: $white) {\n  .right > .direct-chat-text {\n    background-color: $bg-color;\n    border-color: $bg-color;\n    color: color-yiq($bg-color);\n\n    &::after,\n    &::before {\n      border-left-color: $bg-color;\n    }\n  }\n}\n", "//\n// Component: Users List\n//\n\n.users-list {\n  @include list-unstyled ();\n\n  > li {\n    float: left;\n    padding: 10px;\n    text-align: center;\n    width: 25%;\n\n    img {\n      @include border-radius(50%);\n      height: auto;\n      max-width: 100%;\n    }\n\n    > a:hover {\n      &,\n      .users-list-name {\n        color: #999;\n      }\n    }\n  }\n}\n\n.users-list-name,\n.users-list-date {\n  display: block;\n}\n\n.users-list-name {\n  color: $gray-700;\n  font-size: $font-size-sm;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.users-list-date {\n  color: darken($gray-500, 20%);\n  font-size: 12px;\n}\n\n.dark-mode {\n  .users-list-name {\n    color: $gray-400;\n  }\n  .users-list-date {\n    color: $gray-500;\n  }\n}\n", "//\n// Component: Social Widgets\n//\n\n//General widget style\n.card-widget {\n  border: 0;\n  position: relative;\n}\n\n//User Widget Style 1\n.widget-user {\n\n  //User name container\n  .widget-user-header {\n    @if $enable-rounded {\n      @include border-top-radius($border-radius);\n    }\n\n    height: 135px;\n    padding: 1rem;\n    text-align: center;\n  }\n\n  //User name\n  .widget-user-username {\n    font-size: 25px;\n    font-weight: 300;\n    margin-bottom: 0;\n    margin-top: 0;\n    text-shadow: 0 1px 1px rgba($black, .2);\n  }\n\n  //User single line description\n  .widget-user-desc {\n    margin-top: 0;\n  }\n\n  //User image container\n  .widget-user-image {\n    left: 50%;\n    margin-left: -45px;\n    position: absolute;\n    top: 80px;\n\n    > img {\n      border: 3px solid $white;\n      height: auto;\n      width: 90px;\n    }\n  }\n\n  .card-footer {\n    padding-top: 50px;\n  }\n}\n\n//User Widget Style 2\n.widget-user-2 {\n\n  //User name container\n  .widget-user-header {\n    @include border-top-radius($border-radius);\n    padding: 1rem;\n  }\n\n  //User name\n  .widget-user-username {\n    font-size: 25px;\n    font-weight: 300;\n    margin-bottom: 5px;\n    margin-top: 5px;\n  }\n\n  //User single line description\n  .widget-user-desc {\n    margin-top: 0;\n  }\n\n  .widget-user-username,\n  .widget-user-desc {\n    margin-left: 75px;\n  }\n\n  //User image container\n  .widget-user-image {\n    > img {\n      float: left;\n      height: auto;\n      width: 65px;\n    }\n  }\n}\n"]}