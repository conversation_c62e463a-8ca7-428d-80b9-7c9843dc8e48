<?php
require_once(__DIR__.'/../../config.php');
require_once(__DIR__.'/../../libs/db.php');
require_once(__DIR__.'/../../libs/helper.php');

$CMSNT = new DB();

// Kiểm tra đăng nhập admin
if (empty($_SESSION['admin'])) {
    die(json_encode(['status' => 'error', 'message' => 'Vui lòng đăng nhập!']));
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    die(json_encode(['status' => 'error', 'message' => 'Method not allowed']));
}

$id = (int)$_POST['id'];

// Kiểm tra bài viết có tồn tại
$post = $CMSNT->get_row("SELECT * FROM forum_posts WHERE id = '$id'");
if (!$post) {
    die(json_encode(['status' => 'error', 'message' => 'Bài viết không tồn tại']));
}

try {
    // Xóa các bình luận của bài viết
    $CMSNT->remove('forum_comments', " post_id = '$id' ");
    
    // Xóa các like của bài viết
    $CMSNT->remove('forum_post_likes', " post_id = '$id' ");
    
    // Xóa bài viết
    $CMSNT->remove('forum_posts', " id = '$id' ");
    
    die(json_encode([
        'status' => 'success',
        'message' => 'Xóa bài viết thành công!'
    ]));
} catch (Exception $e) {
    die(json_encode(['status' => 'error', 'message' => 'Có lỗi xảy ra: ' . $e->getMessage()]));
}
?>
