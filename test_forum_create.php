<?php
// Simple test file to debug forum creation
session_start();

// Simulate a logged in user for testing
if (!isset($_SESSION['login'])) {
    // You need to set this to a valid user token from your database
    // $_SESSION['login'] = 'your_user_token_here';
    echo "Please set a valid user token in this file to test.";
    exit;
}

require_once(__DIR__.'/config.php');
require_once(__DIR__.'/libs/db.php');
require_once(__DIR__.'/libs/helper.php');

$CMSNT = new DB();

echo "<h1>Forum Create Post Test</h1>";
echo "<style>body { font-family: Arial; margin: 20px; }</style>";

// Test data
$test_data = [
    'category_id' => 1,
    'title' => 'Test Post - ' . date('Y-m-d H:i:s'),
    'content' => '<p>This is a test post created at ' . date('Y-m-d H:i:s') . '</p>',
    'images' => '',
    'download_link' => '',
    'download_password' => '',
    'is_link_hidden' => 1,
    'tags' => 'test,debug',
    'notify_followers' => 0,
    'allow_comments' => 1,
    'pin_request' => 0
];

echo "<h2>Test Data:</h2>";
echo "<pre>" . print_r($test_data, true) . "</pre>";

// Simulate POST request
$_POST = $test_data;
$_SERVER['REQUEST_METHOD'] = 'POST';

echo "<h2>Simulating AJAX Request...</h2>";

// Capture output
ob_start();
include(__DIR__ . '/ajaxs/client/forum-create-post.php');
$output = ob_get_clean();

echo "<h2>Response:</h2>";
echo "<pre>" . htmlspecialchars($output) . "</pre>";

// Try to decode JSON
$response = json_decode($output, true);
if ($response) {
    echo "<h2>Parsed Response:</h2>";
    echo "<pre>" . print_r($response, true) . "</pre>";
    
    if ($response['status'] === 'success') {
        echo "<div style='color: green; background: #e8f5e8; padding: 10px; border-radius: 5px;'>";
        echo "✅ SUCCESS: " . $response['message'];
        if (isset($response['post_id'])) {
            echo "<br>Post ID: " . $response['post_id'];
        }
        echo "</div>";
    } else {
        echo "<div style='color: red; background: #ffe8e8; padding: 10px; border-radius: 5px;'>";
        echo "❌ ERROR: " . $response['message'];
        echo "</div>";
    }
} else {
    echo "<div style='color: orange; background: #fff3cd; padding: 10px; border-radius: 5px;'>";
    echo "⚠️ Could not parse JSON response";
    echo "</div>";
}
?>
