<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forum AJAX Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            background: #fafafa;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-danger {
            background: #dc3545;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .form-group {
            margin: 10px 0;
        }
        .form-control {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Forum AJAX Test Suite</h1>
        
        <div class="test-section">
            <h2>📝 Test Create Post AJAX</h2>
            <form id="testCreateForm">
                <div class="form-group">
                    <label for="category_id">Category ID:</label>
                    <input type="number" id="category_id" name="category_id" class="form-control" value="1">
                </div>
                <div class="form-group">
                    <label for="title">Title:</label>
                    <input type="text" id="title" name="title" class="form-control" value="Test Post Title">
                </div>
                <div class="form-group">
                    <label for="content">Content:</label>
                    <textarea id="content" name="content" class="form-control" rows="4">This is a test post content.</textarea>
                </div>
                <button type="button" class="btn btn-success" onclick="testCreatePost()">Test Create Post</button>
            </form>
            <div id="createResult"></div>
        </div>

        <div class="test-section">
            <h2>🔧 Test Admin Actions</h2>
            <div class="form-group">
                <label for="post_id">Post ID:</label>
                <input type="number" id="post_id" class="form-control" value="1">
            </div>
            <button type="button" class="btn" onclick="testAdminAction('approve')">Approve Post</button>
            <button type="button" class="btn" onclick="testAdminAction('unapprove')">Unapprove Post</button>
            <button type="button" class="btn" onclick="testAdminAction('pin')">Pin Post</button>
            <button type="button" class="btn" onclick="testAdminAction('unpin')">Unpin Post</button>
            <button type="button" class="btn btn-danger" onclick="testAdminAction('delete')">Delete Post</button>
            <div id="adminResult"></div>
        </div>

        <div class="test-section">
            <h2>🌐 Test Endpoints</h2>
            <button type="button" class="btn" onclick="testEndpoint('/ajaxs/client/forum-create-post.php')">Test Create Endpoint</button>
            <button type="button" class="btn" onclick="testEndpoint('/ajaxs/admin/forum-post-actions.php')">Test Admin Endpoint</button>
            <div id="endpointResult"></div>
        </div>

        <div class="test-section">
            <h2>📊 Console Logs</h2>
            <button type="button" class="btn" onclick="clearLogs()">Clear Logs</button>
            <div id="consoleLogs" style="background: #000; color: #0f0; padding: 10px; font-family: monospace; height: 200px; overflow-y: auto;"></div>
        </div>
    </div>

    <script>
        // Override console.log to display in our div
        const originalLog = console.log;
        const originalError = console.error;
        
        function addLog(message, type = 'log') {
            const logsDiv = document.getElementById('consoleLogs');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#f00' : '#0f0';
            logsDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logsDiv.scrollTop = logsDiv.scrollHeight;
            
            // Also log to real console
            if (type === 'error') {
                originalError(message);
            } else {
                originalLog(message);
            }
        }
        
        console.log = function(message) {
            addLog(message, 'log');
        };
        
        console.error = function(message) {
            addLog(message, 'error');
        };

        function clearLogs() {
            document.getElementById('consoleLogs').innerHTML = '';
        }

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function testCreatePost() {
            console.log('Testing create post...');
            
            const formData = {
                category_id: document.getElementById('category_id').value,
                title: document.getElementById('title').value,
                content: document.getElementById('content').value,
                images: '',
                download_link: '',
                download_password: '',
                is_link_hidden: 1,
                tags: 'test,ajax',
                notify_followers: 0,
                allow_comments: 1,
                pin_request: 0
            };

            console.log('Form data:', JSON.stringify(formData));

            $.ajax({
                url: '/ajaxs/client/forum-create-post.php',
                type: 'POST',
                dataType: 'JSON',
                data: formData,
                beforeSend: function() {
                    console.log('Sending AJAX request...');
                    showResult('createResult', 'Sending request...', 'info');
                },
                success: function(data) {
                    console.log('AJAX Success:', JSON.stringify(data));
                    if (data && data.status === 'success') {
                        showResult('createResult', `✅ Success: ${data.message}`, 'success');
                    } else {
                        showResult('createResult', `❌ Error: ${data ? data.message : 'Unknown error'}`, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', xhr.responseText);
                    console.error('Status:', status);
                    console.error('Error:', error);
                    showResult('createResult', `❌ AJAX Error: ${xhr.responseText || error}`, 'error');
                }
            });
        }

        function testAdminAction(action) {
            console.log(`Testing admin action: ${action}`);
            
            const postId = document.getElementById('post_id').value;
            
            $.ajax({
                url: '/ajaxs/admin/forum-post-actions.php',
                type: 'POST',
                dataType: 'JSON',
                data: {
                    action: action,
                    post_id: postId
                },
                beforeSend: function() {
                    console.log(`Sending admin action: ${action} for post ${postId}`);
                    showResult('adminResult', `Sending ${action} request...`, 'info');
                },
                success: function(data) {
                    console.log('Admin Action Success:', JSON.stringify(data));
                    if (data && data.status === 'success') {
                        showResult('adminResult', `✅ Success: ${data.message}`, 'success');
                    } else {
                        showResult('adminResult', `❌ Error: ${data ? data.message : 'Unknown error'}`, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Admin Action Error:', xhr.responseText);
                    showResult('adminResult', `❌ AJAX Error: ${xhr.responseText || error}`, 'error');
                }
            });
        }

        function testEndpoint(url) {
            console.log(`Testing endpoint: ${url}`);
            
            $.ajax({
                url: url,
                type: 'GET',
                beforeSend: function() {
                    showResult('endpointResult', `Testing ${url}...`, 'info');
                },
                success: function(data, status, xhr) {
                    console.log(`Endpoint ${url} responded with status: ${xhr.status}`);
                    showResult('endpointResult', `✅ Endpoint ${url} is accessible (Status: ${xhr.status})`, 'success');
                },
                error: function(xhr, status, error) {
                    console.error(`Endpoint ${url} error:`, xhr.responseText);
                    showResult('endpointResult', `❌ Endpoint ${url} error: ${xhr.status} - ${error}`, 'error');
                }
            });
        }

        // Initialize
        console.log('Forum AJAX Test Suite loaded');
        console.log('Ready to test forum functionality');
    </script>
</body>
</html>
