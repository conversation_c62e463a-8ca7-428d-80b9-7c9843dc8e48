<?php
if (!defined('IN_SITE')) {
    die('The Request Not Found');
}

require_once(__DIR__.'/../../libs/db.php');
require_once(__DIR__.'/../../config.php');
require_once(__DIR__.'/../../libs/helper.php');

$CMSNT = new DB();

// Kiểm tra đăng nhập
if (isset($_COOKIE["token"])) {
    $getUser = $CMSNT->get_row(" SELECT * FROM `users` WHERE `token` = '" . check_string($_COOKIE['token']) . "' ");
    if (!$getUser) {
        die(json_encode(['status' => 'error', 'message' => 'Vui lòng đăng nhập']));
    }
} else {
    die(json_encode(['status' => 'error', 'message' => 'Vui lòng đăng nhập']));
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    die(json_encode(['status' => 'error', 'message' => 'Method not allowed']));
}

$post_id = (int)$_POST['post_id'];
$content = trim($_POST['content']);
$parent_id = isset($_POST['parent_id']) ? (int)$_POST['parent_id'] : null;

// Validate dữ liệu
if (empty($content)) {
    die(json_encode(['status' => 'error', 'message' => 'Vui lòng nhập nội dung bình luận']));
}

// Kiểm tra bài viết có tồn tại và không bị khóa
$post = $CMSNT->get_row("SELECT * FROM forum_posts WHERE id = '$post_id' AND status = 1");
if (!$post) {
    die(json_encode(['status' => 'error', 'message' => 'Bài viết không tồn tại']));
}

if ($post['is_locked']) {
    die(json_encode(['status' => 'error', 'message' => 'Bài viết đã bị khóa, không thể bình luận']));
}

// Kiểm tra comment cha nếu có
if ($parent_id) {
    $parent_comment = $CMSNT->get_row("SELECT * FROM forum_comments WHERE id = '$parent_id' AND post_id = '$post_id' AND status = 1");
    if (!$parent_comment) {
        die(json_encode(['status' => 'error', 'message' => 'Bình luận cha không tồn tại']));
    }
}

$content = check_string($content);

try {
    // Tạo bình luận mới
    $insert = $CMSNT->insert('forum_comments', [
        'post_id' => $post_id,
        'user_id' => $getUser['id'],
        'parent_id' => $parent_id,
        'content' => $content,
        'status' => 1,
        'created_at' => gmdate('Y-m-d H:i:s', time() + 7*3600),
        'updated_at' => gmdate('Y-m-d H:i:s', time() + 7*3600)
    ]);

    if ($insert) {
        // Cập nhật số lượng bình luận trong bài viết
        $new_comment_count = $post['comments_count'] + 1;
        $CMSNT->update('forum_posts', [
            'comments_count' => $new_comment_count,
            'updated_at' => gmdate('Y-m-d H:i:s', time() + 7*3600)
        ], " id = '$post_id' ");

        die(json_encode([
            'status' => 'success',
            'message' => 'Đã gửi bình luận thành công!',
            'comment_id' => $CMSNT->lastInsertId()
        ]));
    } else {
        die(json_encode(['status' => 'error', 'message' => 'Có lỗi xảy ra khi gửi bình luận']));
    }
} catch (Exception $e) {
    die(json_encode(['status' => 'error', 'message' => 'Có lỗi xảy ra: ' . $e->getMessage()]));
}
?>
