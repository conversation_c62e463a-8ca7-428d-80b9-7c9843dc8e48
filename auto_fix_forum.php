<?php
// Auto-fix forum issues and create sample data
require_once(__DIR__.'/config.php');
require_once(__DIR__.'/libs/db.php');
require_once(__DIR__.'/libs/helper.php');

$CMSNT = new DB();

echo "<h1>🔧 Auto-Fix Forum System</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: green; background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
.error { color: red; background: #ffe8e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
.info { color: blue; background: #e8f0ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
.warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
</style>";

// Step 1: Check and create categories if needed
echo "<h2>📁 Step 1: Check Categories</h2>";
$categories = $CMSNT->get_list("SELECT * FROM forum_categories WHERE status = 1");
if (count($categories) == 0) {
    echo "<div class='warning'>⚠️ No active categories found. Creating sample categories...</div>";
    
    $sample_categories = [
        ['name' => 'Thảo luận chung', 'description' => 'Nơi trao đổi các chủ đề tổng quát', 'icon' => 'fas fa-comments', 'position' => 1],
        ['name' => 'Chia sẻ mã nguồn', 'description' => 'Chia sẻ và trao đổi mã nguồn game bài', 'icon' => 'fas fa-code', 'position' => 2],
        ['name' => 'Hướng dẫn - Tutorial', 'description' => 'Các bài hướng dẫn lập trình', 'icon' => 'fas fa-book', 'position' => 3],
        ['name' => 'Báo lỗi - Hỗ trợ', 'description' => 'Báo cáo lỗi và yêu cầu hỗ trợ', 'icon' => 'fas fa-bug', 'position' => 4]
    ];
    
    foreach ($sample_categories as $cat) {
        $insert = $CMSNT->insert('forum_categories', [
            'name' => $cat['name'],
            'description' => $cat['description'],
            'icon' => $cat['icon'],
            'position' => $cat['position'],
            'status' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        if ($insert) {
            echo "<div class='success'>✅ Created category: {$cat['name']}</div>";
        } else {
            echo "<div class='error'>❌ Failed to create category: {$cat['name']}</div>";
        }
    }
} else {
    echo "<div class='success'>✅ Found " . count($categories) . " active categories</div>";
}

// Step 2: Check admin user
echo "<h2>👤 Step 2: Check Admin User</h2>";
$admin_user = $CMSNT->get_row("SELECT * FROM users WHERE admin = 1 LIMIT 1");
if (!$admin_user) {
    echo "<div class='error'>❌ No admin user found. Cannot create sample posts.</div>";
    echo "<div class='info'>ℹ️ Please make sure you have at least one admin user in the system.</div>";
} else {
    echo "<div class='success'>✅ Admin user found: {$admin_user['username']}</div>";
    
    // Step 3: Create sample posts if needed
    echo "<h2>📝 Step 3: Create Sample Posts</h2>";
    
    $categories = $CMSNT->get_list("SELECT * FROM forum_categories WHERE status = 1 ORDER BY position ASC");
    
    foreach ($categories as $category) {
        $posts_in_cat = $CMSNT->num_rows("SELECT * FROM forum_posts WHERE category_id = '".$category['id']."' AND status = 1");
        
        if ($posts_in_cat == 0) {
            echo "<div class='warning'>⚠️ Category '{$category['name']}' has no approved posts. Creating sample post...</div>";
            
            $sample_posts = [
                1 => [
                    'title' => 'Chào mừng đến với ' . $category['name'],
                    'content' => '<h2>🎉 Chào mừng bạn đến với danh mục ' . $category['name'] . '</h2>
                                 <p>Đây là bài viết mẫu để bạn có thể thấy cách danh mục hoạt động.</p>
                                 <p><strong>Trong danh mục này, bạn có thể:</strong></p>
                                 <ul>
                                 <li>Chia sẻ kiến thức và kinh nghiệm</li>
                                 <li>Đặt câu hỏi và nhận hỗ trợ</li>
                                 <li>Trao đổi với cộng đồng</li>
                                 </ul>
                                 <p>Hãy tham gia và đóng góp nội dung có giá trị! 🚀</p>',
                    'tags' => 'welcome,sample,guide'
                ],
                2 => [
                    'title' => 'Hướng dẫn chia sẻ mã nguồn hiệu quả',
                    'content' => '<h2>📝 Hướng dẫn chia sẻ mã nguồn</h2>
                                 <p>Để chia sẻ mã nguồn hiệu quả, bạn nên:</p>
                                 <h3>✅ Nên làm:</h3>
                                 <ul>
                                 <li>Mô tả rõ ràng chức năng</li>
                                 <li>Cung cấp hướng dẫn cài đặt</li>
                                 <li>Đính kèm screenshot</li>
                                 <li>Ghi rõ yêu cầu hệ thống</li>
                                 </ul>
                                 <h3>❌ Không nên:</h3>
                                 <ul>
                                 <li>Chia sẻ code có virus</li>
                                 <li>Copy paste không ghi nguồn</li>
                                 <li>Spam cùng nội dung</li>
                                 </ul>',
                    'tags' => 'guide,code,sharing,best-practices'
                ],
                3 => [
                    'title' => 'Tổng hợp tutorial lập trình cơ bản',
                    'content' => '<h2>📚 Tutorial lập trình cơ bản</h2>
                                 <p>Danh sách các tutorial hữu ích cho người mới bắt đầu:</p>
                                 <h3>🌟 Web Development:</h3>
                                 <ul>
                                 <li>HTML/CSS cơ bản</li>
                                 <li>JavaScript fundamentals</li>
                                 <li>PHP và MySQL</li>
                                 <li>Framework phổ biến</li>
                                 </ul>
                                 <h3>🎮 Game Development:</h3>
                                 <ul>
                                 <li>Logic game bài cơ bản</li>
                                 <li>Xử lý random và shuffle</li>
                                 <li>Database design cho game</li>
                                 </ul>',
                    'tags' => 'tutorial,programming,beginner,web,game'
                ],
                4 => [
                    'title' => 'Cách báo lỗi và yêu cầu hỗ trợ hiệu quả',
                    'content' => '<h2>🐛 Hướng dẫn báo lỗi</h2>
                                 <p>Để được hỗ trợ nhanh chóng, hãy cung cấp:</p>
                                 <h3>📋 Thông tin cần thiết:</h3>
                                 <ul>
                                 <li>Mô tả chi tiết lỗi</li>
                                 <li>Các bước tái hiện lỗi</li>
                                 <li>Screenshot hoặc video</li>
                                 <li>Thông tin hệ thống</li>
                                 <li>Log lỗi (nếu có)</li>
                                 </ul>
                                 <h3>💡 Mẹo:</h3>
                                 <p>Càng chi tiết càng dễ được hỗ trợ nhanh!</p>',
                    'tags' => 'bug-report,support,help,guide'
                ]
            ];
            
            $post_data = $sample_posts[$category['position']] ?? $sample_posts[1];
            
            $insert = $CMSNT->insert('forum_posts', [
                'category_id' => $category['id'],
                'user_id' => $admin_user['id'],
                'title' => $post_data['title'],
                'content' => $post_data['content'],
                'tags' => $post_data['tags'],
                'status' => 1, // Auto-approve for admin
                'allow_comments' => 1,
                'is_pinned' => 0,
                'views' => rand(10, 100),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            if ($insert) {
                echo "<div class='success'>✅ Created sample post in '{$category['name']}'</div>";
            } else {
                echo "<div class='error'>❌ Failed to create sample post in '{$category['name']}'</div>";
            }
        } else {
            echo "<div class='success'>✅ Category '{$category['name']}' has $posts_in_cat approved posts</div>";
        }
    }
}

// Step 4: Auto-approve pending posts (optional)
echo "<h2>✅ Step 4: Check Pending Posts</h2>";
$pending_posts = $CMSNT->get_list("SELECT p.*, c.name as category_name FROM forum_posts p 
                                   JOIN forum_categories c ON p.category_id = c.id 
                                   WHERE p.status = 0 ORDER BY p.created_at DESC LIMIT 10");

if (count($pending_posts) > 0) {
    echo "<div class='warning'>⚠️ Found " . count($pending_posts) . " pending posts</div>";
    echo "<div class='info'>";
    echo "<p><strong>Pending posts:</strong></p>";
    foreach ($pending_posts as $post) {
        echo "<p>📄 {$post['title']} (Category: {$post['category_name']}) - Created: " . date('d/m/Y H:i', strtotime($post['created_at'])) . "</p>";
    }
    echo "<p><a href='?module=admin&action=forum-posts' target='_blank' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Go to Admin Panel to Approve</a></p>";
    echo "</div>";
} else {
    echo "<div class='success'>✅ No pending posts found</div>";
}

// Step 5: Test links
echo "<h2>🔗 Step 5: Test Links</h2>";
echo "<div class='info'>";
echo "<h3>Quick Test Links:</h3>";
$categories = $CMSNT->get_list("SELECT * FROM forum_categories WHERE status = 1 ORDER BY position ASC");
foreach ($categories as $cat) {
    $posts_count = $CMSNT->num_rows("SELECT * FROM forum_posts WHERE category_id = '".$cat['id']."' AND status = 1");
    echo "<p>📁 <a href='?module=client&action=forum-category&id={$cat['id']}' target='_blank'>{$cat['name']}</a> ($posts_count posts)</p>";
}

echo "<h3>Other Links:</h3>";
echo "<p>🏠 <a href='?module=client&action=forum' target='_blank'>Forum Home</a></p>";
echo "<p>✏️ <a href='?module=client&action=forum-create-post' target='_blank'>Create New Post</a></p>";
echo "<p>👨‍💼 <a href='?module=admin&action=forum-posts' target='_blank'>Admin Panel</a></p>";
echo "</div>";

echo "<div class='success'>";
echo "<h3>🎉 Auto-fix completed!</h3>";
echo "<p>Your forum should now be working properly. Try clicking the test links above.</p>";
echo "<p><strong>Completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "</div>";
?>
