/*!
 *   AdminLTE v3.1.0
 *     Only Pages
 *   Author: Colorlib
 *   Website: AdminLTE.io <https://adminlte.io>
 *   License: Open source - MIT <https://opensource.org/licenses/MIT>
 */.close,.mailbox-attachment-close{float:right;font-size:1.5rem;font-weight:700;line-height:1;color:#000;text-shadow:0 1px 0 #fff;opacity:.5}.close:hover,.mailbox-attachment-close:hover{color:#000;text-decoration:none}.close:not(:disabled):not(.disabled):focus,.close:not(:disabled):not(.disabled):hover,.mailbox-attachment-close:not(:disabled):not(.disabled):focus,.mailbox-attachment-close:not(:disabled):not(.disabled):hover{opacity:.75}button.close,button.mailbox-attachment-close{padding:0;background-color:transparent;border:0}a.close.disabled,a.disabled.mailbox-attachment-close{pointer-events:none}@-webkit-keyframes flipInX{0%{-webkit-transform:perspective(400px) rotate3d(1,0,0,90deg);transform:perspective(400px) rotate3d(1,0,0,90deg);transition-timing-function:ease-in;opacity:0}40%{-webkit-transform:perspective(400px) rotate3d(1,0,0,-20deg);transform:perspective(400px) rotate3d(1,0,0,-20deg);transition-timing-function:ease-in}60%{-webkit-transform:perspective(400px) rotate3d(1,0,0,10deg);transform:perspective(400px) rotate3d(1,0,0,10deg);opacity:1}80%{-webkit-transform:perspective(400px) rotate3d(1,0,0,-5deg);transform:perspective(400px) rotate3d(1,0,0,-5deg)}100%{-webkit-transform:perspective(400px);transform:perspective(400px)}}@keyframes flipInX{0%{-webkit-transform:perspective(400px) rotate3d(1,0,0,90deg);transform:perspective(400px) rotate3d(1,0,0,90deg);transition-timing-function:ease-in;opacity:0}40%{-webkit-transform:perspective(400px) rotate3d(1,0,0,-20deg);transform:perspective(400px) rotate3d(1,0,0,-20deg);transition-timing-function:ease-in}60%{-webkit-transform:perspective(400px) rotate3d(1,0,0,10deg);transform:perspective(400px) rotate3d(1,0,0,10deg);opacity:1}80%{-webkit-transform:perspective(400px) rotate3d(1,0,0,-5deg);transform:perspective(400px) rotate3d(1,0,0,-5deg)}100%{-webkit-transform:perspective(400px);transform:perspective(400px)}}@-webkit-keyframes fadeIn{from{opacity:0}to{opacity:1}}@keyframes fadeIn{from{opacity:0}to{opacity:1}}@-webkit-keyframes fadeOut{from{opacity:1}to{opacity:0}}@keyframes fadeOut{from{opacity:1}to{opacity:0}}@-webkit-keyframes shake{0%{-webkit-transform:translate(2px,1px) rotate(0);transform:translate(2px,1px) rotate(0)}10%{-webkit-transform:translate(-1px,-2px) rotate(-2deg);transform:translate(-1px,-2px) rotate(-2deg)}20%{-webkit-transform:translate(-3px,0) rotate(3deg);transform:translate(-3px,0) rotate(3deg)}30%{-webkit-transform:translate(0,2px) rotate(0);transform:translate(0,2px) rotate(0)}40%{-webkit-transform:translate(1px,-1px) rotate(1deg);transform:translate(1px,-1px) rotate(1deg)}50%{-webkit-transform:translate(-1px,2px) rotate(-1deg);transform:translate(-1px,2px) rotate(-1deg)}60%{-webkit-transform:translate(-3px,1px) rotate(0);transform:translate(-3px,1px) rotate(0)}70%{-webkit-transform:translate(2px,1px) rotate(-2deg);transform:translate(2px,1px) rotate(-2deg)}80%{-webkit-transform:translate(-1px,-1px) rotate(4deg);transform:translate(-1px,-1px) rotate(4deg)}90%{-webkit-transform:translate(2px,2px) rotate(0);transform:translate(2px,2px) rotate(0)}100%{-webkit-transform:translate(1px,-2px) rotate(-1deg);transform:translate(1px,-2px) rotate(-1deg)}}@keyframes shake{0%{-webkit-transform:translate(2px,1px) rotate(0);transform:translate(2px,1px) rotate(0)}10%{-webkit-transform:translate(-1px,-2px) rotate(-2deg);transform:translate(-1px,-2px) rotate(-2deg)}20%{-webkit-transform:translate(-3px,0) rotate(3deg);transform:translate(-3px,0) rotate(3deg)}30%{-webkit-transform:translate(0,2px) rotate(0);transform:translate(0,2px) rotate(0)}40%{-webkit-transform:translate(1px,-1px) rotate(1deg);transform:translate(1px,-1px) rotate(1deg)}50%{-webkit-transform:translate(-1px,2px) rotate(-1deg);transform:translate(-1px,2px) rotate(-1deg)}60%{-webkit-transform:translate(-3px,1px) rotate(0);transform:translate(-3px,1px) rotate(0)}70%{-webkit-transform:translate(2px,1px) rotate(-2deg);transform:translate(2px,1px) rotate(-2deg)}80%{-webkit-transform:translate(-1px,-1px) rotate(4deg);transform:translate(-1px,-1px) rotate(4deg)}90%{-webkit-transform:translate(2px,2px) rotate(0);transform:translate(2px,2px) rotate(0)}100%{-webkit-transform:translate(1px,-2px) rotate(-1deg);transform:translate(1px,-2px) rotate(-1deg)}}@-webkit-keyframes wobble{0%{-webkit-transform:none;transform:none}15%{-webkit-transform:translate3d(-25%,0,0) rotate3d(0,0,1,-5deg);transform:translate3d(-25%,0,0) rotate3d(0,0,1,-5deg)}30%{-webkit-transform:translate3d(20%,0,0) rotate3d(0,0,1,3deg);transform:translate3d(20%,0,0) rotate3d(0,0,1,3deg)}45%{-webkit-transform:translate3d(-15%,0,0) rotate3d(0,0,1,-3deg);transform:translate3d(-15%,0,0) rotate3d(0,0,1,-3deg)}60%{-webkit-transform:translate3d(10%,0,0) rotate3d(0,0,1,2deg);transform:translate3d(10%,0,0) rotate3d(0,0,1,2deg)}75%{-webkit-transform:translate3d(-5%,0,0) rotate3d(0,0,1,-1deg);transform:translate3d(-5%,0,0) rotate3d(0,0,1,-1deg)}100%{-webkit-transform:none;transform:none}}@keyframes wobble{0%{-webkit-transform:none;transform:none}15%{-webkit-transform:translate3d(-25%,0,0) rotate3d(0,0,1,-5deg);transform:translate3d(-25%,0,0) rotate3d(0,0,1,-5deg)}30%{-webkit-transform:translate3d(20%,0,0) rotate3d(0,0,1,3deg);transform:translate3d(20%,0,0) rotate3d(0,0,1,3deg)}45%{-webkit-transform:translate3d(-15%,0,0) rotate3d(0,0,1,-3deg);transform:translate3d(-15%,0,0) rotate3d(0,0,1,-3deg)}60%{-webkit-transform:translate3d(10%,0,0) rotate3d(0,0,1,2deg);transform:translate3d(10%,0,0) rotate3d(0,0,1,2deg)}75%{-webkit-transform:translate3d(-5%,0,0) rotate3d(0,0,1,-1deg);transform:translate3d(-5%,0,0) rotate3d(0,0,1,-1deg)}100%{-webkit-transform:none;transform:none}}.mailbox-messages>.table{margin:0}.mailbox-controls{padding:5px}.mailbox-controls.with-border{border-bottom:1px solid rgba(0,0,0,.125)}.mailbox-read-info{border-bottom:1px solid rgba(0,0,0,.125);padding:10px}.mailbox-read-info h3{font-size:20px;margin:0}.mailbox-read-info h5{margin:0;padding:5px 0 0}.mailbox-read-time{color:#999;font-size:13px}.mailbox-read-message{padding:10px}.mailbox-attachments{padding-left:0;list-style:none}.mailbox-attachments li{border:1px solid #eee;float:left;margin-bottom:10px;margin-right:10px;width:200px}.mailbox-attachment-name{color:#666;font-weight:700}.mailbox-attachment-icon,.mailbox-attachment-info,.mailbox-attachment-size{display:block}.mailbox-attachment-info{background-color:#f8f9fa;padding:10px}.mailbox-attachment-size{color:#999;font-size:12px}.mailbox-attachment-size>span{display:inline-block;padding-top:.75rem}.mailbox-attachment-icon{color:#666;font-size:65px;max-height:132.5px;padding:20px 10px;text-align:center}.mailbox-attachment-icon.has-img{padding:0}.mailbox-attachment-icon.has-img>img{height:auto;max-width:100%}.lockscreen{background-color:#e9ecef}.lockscreen .lockscreen-name{font-weight:600;text-align:center}.lockscreen-logo{font-size:35px;font-weight:300;margin-bottom:25px;text-align:center}.lockscreen-logo a{color:#495057}.lockscreen-wrapper{margin:0 auto;margin-top:10%;max-width:400px}.lockscreen-item{border-radius:4px;background-color:#fff;margin:10px auto 30px;padding:0;position:relative;width:290px}.lockscreen-image{border-radius:50%;background-color:#fff;left:-10px;padding:5px;position:absolute;top:-25px;z-index:10}.lockscreen-image>img{border-radius:50%;height:70px;width:70px}.lockscreen-credentials{margin-left:70px}.lockscreen-credentials .form-control{border:0}.lockscreen-credentials .btn{background-color:#fff;border:0;padding:0 10px}.lockscreen-footer{margin-top:10px}.dark-mode .lockscreen-item{background-color:#343a40}.dark-mode .lockscreen-logo a{color:#fff}.dark-mode .lockscreen-credentials .btn{background-color:#343a40}.dark-mode .lockscreen-image{background-color:#6c757d}.login-logo,.register-logo{font-size:2.1rem;font-weight:300;margin-bottom:.9rem;text-align:center}.login-logo a,.register-logo a{color:#495057}.login-page,.register-page{-webkit-align-items:center;-ms-flex-align:center;align-items:center;background-color:#e9ecef;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;height:100vh;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center}.login-box,.register-box{width:360px}@media (max-width:576px){.login-box,.register-box{margin-top:.5rem;width:90%}}.login-box .card,.register-box .card{margin-bottom:0}.login-card-body,.register-card-body{background-color:#fff;border-top:0;color:#666;padding:20px}.login-card-body .input-group .form-control,.register-card-body .input-group .form-control{border-right:0}.login-card-body .input-group .form-control:focus,.register-card-body .input-group .form-control:focus{box-shadow:none}.login-card-body .input-group .form-control:focus~.input-group-append .input-group-text,.login-card-body .input-group .form-control:focus~.input-group-prepend .input-group-text,.register-card-body .input-group .form-control:focus~.input-group-append .input-group-text,.register-card-body .input-group .form-control:focus~.input-group-prepend .input-group-text{border-color:#80bdff}.login-card-body .input-group .form-control.is-valid:focus,.register-card-body .input-group .form-control.is-valid:focus{box-shadow:none}.login-card-body .input-group .form-control.is-valid~.input-group-append .input-group-text,.login-card-body .input-group .form-control.is-valid~.input-group-prepend .input-group-text,.register-card-body .input-group .form-control.is-valid~.input-group-append .input-group-text,.register-card-body .input-group .form-control.is-valid~.input-group-prepend .input-group-text{border-color:#28a745}.login-card-body .input-group .form-control.is-invalid:focus,.register-card-body .input-group .form-control.is-invalid:focus{box-shadow:none}.login-card-body .input-group .form-control.is-invalid~.input-group-append .input-group-text,.register-card-body .input-group .form-control.is-invalid~.input-group-append .input-group-text{border-color:#dc3545}.login-card-body .input-group .input-group-text,.register-card-body .input-group .input-group-text{background-color:transparent;border-bottom-right-radius:.25rem;border-left:0;border-top-right-radius:.25rem;color:#777;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out}.login-box-msg,.register-box-msg{margin:0;padding:0 20px 20px;text-align:center}.social-auth-links{margin:10px 0}.dark-mode .login-card-body,.dark-mode .register-card-body{background-color:#343a40;border-color:#6c757d;color:#fff}.dark-mode .login-logo a,.dark-mode .register-logo a{color:#fff}.error-page{margin:20px auto 0;width:600px}@media (max-width:767.98px){.error-page{width:100%}}.error-page>.headline{float:left;font-size:100px;font-weight:300}@media (max-width:767.98px){.error-page>.headline{float:none;text-align:center}}.error-page>.error-content{display:block;margin-left:190px}@media (max-width:767.98px){.error-page>.error-content{margin-left:0}}.error-page>.error-content>h3{font-size:25px;font-weight:300}@media (max-width:767.98px){.error-page>.error-content>h3{text-align:center}}.invoice{background-color:#fff;border:1px solid rgba(0,0,0,.125);position:relative}.invoice-title{margin-top:0}.dark-mode .invoice{background-color:#343a40}.profile-user-img{border:3px solid #adb5bd;margin:0 auto;padding:3px;width:100px}.profile-username{font-size:21px;margin-top:5px}.post{border-bottom:1px solid #adb5bd;color:#666;margin-bottom:15px;padding-bottom:15px}.post:last-of-type{border-bottom:0;margin-bottom:0;padding-bottom:0}.post .user-block{margin-bottom:15px;width:100%}.post .row{width:100%}.dark-mode .post{color:#fff;border-color:#6c757d}.product-image{max-width:100%;height:auto;width:100%}.product-image-thumbs{-webkit-align-items:stretch;-ms-flex-align:stretch;align-items:stretch;display:-webkit-flex;display:-ms-flexbox;display:flex;margin-top:2rem}.product-image-thumb{box-shadow:0 1px 2px rgba(0,0,0,.075);border-radius:.25rem;background-color:#fff;border:1px solid #dee2e6;display:-webkit-flex;display:-ms-flexbox;display:flex;margin-right:1rem;max-width:7rem;padding:.5rem}.product-image-thumb img{max-width:100%;height:auto;-webkit-align-self:center;-ms-flex-item-align:center;align-self:center}.product-image-thumb:hover{opacity:.5}.product-share a{margin-right:.5rem}.projects td{vertical-align:middle}.projects .list-inline{margin-bottom:0}.projects .table-avatar img,.projects img.table-avatar{border-radius:50%;display:inline;width:2.5rem}.projects .project-state{text-align:center}body.iframe-mode .main-sidebar{display:none}body.iframe-mode .content-wrapper{margin-left:0!important;margin-top:0!important;padding-bottom:0!important}body.iframe-mode .main-footer,body.iframe-mode .main-header{display:none}body.iframe-mode-fullscreen{overflow:hidden}.content-wrapper{height:100%}.content-wrapper.iframe-mode .btn-iframe-close{color:#dc3545;position:absolute;line-height:1;right:.125rem;top:.125rem;z-index:10;visibility:hidden}.content-wrapper.iframe-mode .btn-iframe-close:focus,.content-wrapper.iframe-mode .btn-iframe-close:hover{-webkit-animation-name:fadeIn;animation-name:fadeIn;-webkit-animation-duration:.3s;animation-duration:.3s;-webkit-animation-fill-mode:both;animation-fill-mode:both;visibility:visible}@media (hover:none) and (pointer:coarse){.content-wrapper.iframe-mode .btn-iframe-close{visibility:visible}}.content-wrapper.iframe-mode .navbar-nav{overflow-y:auto;width:100%}.content-wrapper.iframe-mode .navbar-nav .nav-link{white-space:nowrap}.content-wrapper.iframe-mode .navbar-nav .nav-item{position:relative}.content-wrapper.iframe-mode .navbar-nav .nav-item:focus .btn-iframe-close,.content-wrapper.iframe-mode .navbar-nav .nav-item:hover .btn-iframe-close{-webkit-animation-name:fadeIn;animation-name:fadeIn;-webkit-animation-duration:.3s;animation-duration:.3s;-webkit-animation-fill-mode:both;animation-fill-mode:both;visibility:visible}@media (hover:none) and (pointer:coarse){.content-wrapper.iframe-mode .navbar-nav .nav-item:focus .btn-iframe-close,.content-wrapper.iframe-mode .navbar-nav .nav-item:hover .btn-iframe-close{visibility:visible}}.content-wrapper.iframe-mode .tab-content{position:relative}.content-wrapper.iframe-mode .tab-pane+.tab-empty{display:none}.content-wrapper.iframe-mode .tab-empty{width:100%;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}.content-wrapper.iframe-mode .tab-loading{position:absolute;top:0;left:0;width:100%;display:none;background-color:#f4f6f9}.content-wrapper.iframe-mode .tab-loading>div{display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;width:100%;height:100%}.content-wrapper.iframe-mode iframe{border:0;width:100%;height:100%;margin-bottom:-8px}.content-wrapper.iframe-mode iframe .content-wrapper{padding-bottom:0!important}body.iframe-mode-fullscreen .content-wrapper.iframe-mode{position:absolute;left:0;top:0;right:0;bottom:0;margin-left:0!important;height:100%;min-height:100%;z-index:1048}.permanent-btn-iframe-close .btn-iframe-close{-webkit-animation:none!important;animation:none!important;visibility:visible!important;opacity:1}.content-wrapper.kanban{height:1px}.content-wrapper.kanban .content{height:100%;overflow-x:auto;overflow-y:hidden}.content-wrapper.kanban .content .container,.content-wrapper.kanban .content .container-fluid{width:-webkit-max-content;width:-moz-max-content;width:max-content;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:stretch;-ms-flex-align:stretch;align-items:stretch}.content-wrapper.kanban .content-header+.content{height:calc(100% - ((2 * 15px) + (1.8rem * 1.2)))}.content-wrapper.kanban .card .card-body{padding:.5rem}.content-wrapper.kanban .card.card-row{width:340px;display:inline-block;margin:0 .5rem}.content-wrapper.kanban .card.card-row:first-child{margin-left:0}.content-wrapper.kanban .card.card-row .card-body{height:calc(100% - (12px + (1.8rem * 1.2) + .5rem));overflow-y:auto}.content-wrapper.kanban .card.card-row .card:last-child{margin-bottom:0;border-bottom-width:1px}.content-wrapper.kanban .card.card-row .card .card-header{padding:.5rem .75rem}.content-wrapper.kanban .card.card-row .card .card-body{padding:.75rem}.content-wrapper.kanban .btn-tool.btn-link{text-decoration:underline;padding-left:0;padding-right:0}
/*# sourceMappingURL=adminlte.pages.min.css.map */