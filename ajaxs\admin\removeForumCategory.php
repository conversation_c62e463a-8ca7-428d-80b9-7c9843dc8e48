<?php

require_once(__DIR__.'/../../config.php');
require_once(__DIR__.'/../../libs/db.php');
require_once(__DIR__.'/../../libs/helper.php');

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (!isset($_SESSION['admin_login'])) {
        die(json_encode(['status' => 'error', 'message' => 'Bạn chưa đăng nhập!']));
    }
    
    $CMSNT = new DB();
    $getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `admin` = 1 AND `token` = '".$_SESSION['admin_login']."'");
    
    if (!$getUser) {
        die(json_encode(['status' => 'error', 'message' => 'Không tìm thấy thông tin admin!']));
    }
    
    $id = (int)$_POST['id'];
    
    if (empty($id)) {
        die(json_encode(['status' => 'error', 'message' => 'Dữ liệu không hợp lệ!']));
    }
    
    $category = $CMSNT->get_row("SELECT * FROM forum_categories WHERE id = '$id'");
    if (!$category) {
        die(json_encode(['status' => 'error', 'message' => 'Danh mục không tồn tại!']));
    }
    
    // Xóa tất cả bài viết trong danh mục
    $posts = $CMSNT->get_list("SELECT id FROM forum_posts WHERE category_id = '$id'");
    foreach ($posts as $post) {
        // Xóa comments của bài viết
        $CMSNT->remove("forum_comments", " post_id = '".$post['id']."' ");
        // Xóa likes của bài viết
        $CMSNT->remove("forum_post_likes", " post_id = '".$post['id']."' ");
    }
    
    // Xóa tất cả bài viết
    $CMSNT->remove("forum_posts", " category_id = '$id' ");
    
    // Xóa danh mục
    $remove = $CMSNT->remove("forum_categories", " id = '$id' ");
    
    if ($remove) {
        die(json_encode(['status' => 'success', 'message' => 'Xóa danh mục thành công!']));
    } else {
        die(json_encode(['status' => 'error', 'message' => 'Có lỗi xảy ra, vui lòng thử lại!']));
    }
}
