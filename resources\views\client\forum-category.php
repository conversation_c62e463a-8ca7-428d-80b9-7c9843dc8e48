<?php if (!defined('IN_SITE')) {
    die('The Request Not Found');
}

// Ki<PERSON>m tra đăng nhập (cho phép xem category không cần đăng nhập)
$isLoggedIn = false;
$currentUser = null;

if (isset($_COOKIE["token"])) {
    $getUser = $CMSNT->get_row(" SELECT * FROM `users` WHERE `token` = '" . check_string($_COOKIE['token']) . "' ");
    if ($getUser) {
        $_SESSION['login'] = $getUser['token'];
        $isLoggedIn = true;
        $currentUser = $getUser;
    }
}

if (isset($_SESSION['login']) && !$currentUser) {
    $currentUser = $CMSNT->get_row(" SELECT * FROM `users` WHERE `token` = '" . check_string($_SESSION['login']) . "' ");
    if ($currentUser) {
        $isLoggedIn = true;
    }
}

$category_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Debug: Log category access
error_log("Forum Category Access - ID: $category_id, Time: " . date('Y-m-d H:i:s'));

$category = $CMSNT->get_row("SELECT * FROM forum_categories WHERE id = '$category_id' AND status = 1");

if (!$category) {
    error_log("Category not found or inactive - ID: $category_id");
    // Thử tìm category không kể status
    $category_any = $CMSNT->get_row("SELECT * FROM forum_categories WHERE id = '$category_id'");
    if ($category_any) {
        error_log("Category exists but inactive - ID: $category_id, Status: " . $category_any['status']);
    } else {
        error_log("Category does not exist - ID: $category_id");
    }
    redirect(base_url('client/forum'));
    exit();
}

error_log("Category found: " . $category['name'] . " (ID: $category_id)");

$body = [
    'title' => $category['name'] . ' - Diễn Đàn - ' . $CMSNT->site('title'),
    'desc'   => $category['description'],
    'keyword' => $CMSNT->site('keywords')
];
$body['header'] = '';
$body['footer'] = '';

require_once(__DIR__.'/header.php');
require_once(__DIR__.'/sidebar.php');
?>

<div class="content-page">
    <div class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between" style="background: <?=$CMSNT->site('theme_color');?>;">
                            <div class="header-title">
                                <h5 class="card-title" style="color:white;">
                                    <i class="<?=$category['icon'];?> mr-2"></i><?=$category['name'];?>
                                </h5>
                            </div>
                            <div>
                                <a href="<?=base_url('client/forum-create-post?category_id='.$category_id);?>" class="btn btn-success btn-sm">
                                    <i class="fas fa-plus mr-1"></i>Đăng bài mới
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Breadcrumb -->
                            <nav aria-label="breadcrumb" class="mb-3">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item">
                                        <a href="<?=base_url('client/forum');?>">Diễn đàn</a>
                                    </li>
                                    <li class="breadcrumb-item active"><?=$category['name'];?></li>
                                </ol>
                            </nav>

                            <?php if($category['description']): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle mr-2"></i><?=$category['description'];?>
                            </div>
                            <?php endif; ?>

                            <!-- Danh sách bài viết -->
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th width="50%">Tiêu đề</th>
                                            <th>Tác giả</th>
                                            <th>Bình luận</th>
                                            <th>Lượt xem</th>
                                            <th>Cập nhật</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php 
                                        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
                                        $limit = 20;
                                        $offset = ($page - 1) * $limit;
                                        
                                        // Debug: Log query details
                                        error_log("Querying posts for category $category_id, page $page, limit $limit, offset $offset");

                                        $posts = $CMSNT->get_list("SELECT p.*, u.username,
                                            (SELECT COUNT(*) FROM forum_comments WHERE post_id = p.id AND status = 1) as comments_count
                                            FROM forum_posts p
                                            JOIN users u ON p.user_id = u.id
                                            WHERE p.category_id = '$category_id' AND p.status = 1
                                            ORDER BY p.is_pinned DESC, p.updated_at DESC
                                            LIMIT $limit OFFSET $offset");

                                        // Debug: Log results
                                        error_log("Found " . count($posts) . " approved posts in category $category_id");

                                        // Also check total posts (including pending)
                                        $total_posts_in_category = $CMSNT->num_rows("SELECT * FROM forum_posts WHERE category_id = '$category_id'");
                                        $pending_posts_in_category = $CMSNT->num_rows("SELECT * FROM forum_posts WHERE category_id = '$category_id' AND status = 0");
                                        error_log("Total posts in category: $total_posts_in_category, Pending: $pending_posts_in_category");
                                        
                                        if(empty($posts)):
                                            // Kiểm tra xem có bài viết chờ duyệt không
                                            $pending_posts = $CMSNT->num_rows("SELECT * FROM forum_posts WHERE category_id = '$category_id' AND status = 0");
                                        ?>
                                        <tr>
                                            <td colspan="5" class="text-center py-5">
                                                <div style="padding: 2rem;">
                                                    <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                                                    <h5 class="text-muted mb-3">Chưa có bài viết nào trong danh mục này</h5>

                                                    <?php if ($pending_posts > 0): ?>
                                                    <div class="alert alert-info mb-3">
                                                        <i class="fas fa-clock mr-2"></i>
                                                        Có <strong><?=$pending_posts;?></strong> bài viết đang chờ admin duyệt
                                                    </div>
                                                    <?php endif; ?>

                                                    <p class="text-muted mb-4">
                                                        Hãy là người đầu tiên chia sẻ kiến thức trong danh mục <strong><?=$category['name'];?></strong>
                                                    </p>

                                                    <?php if ($isLoggedIn): ?>
                                                    <a href="<?=base_url('client/forum-create-post?category_id='.$category_id);?>"
                                                       class="btn btn-primary btn-lg">
                                                        <i class="fas fa-plus mr-2"></i>Đăng bài đầu tiên
                                                    </a>
                                                    <?php else: ?>
                                                    <a href="<?=base_url('client/login');?>"
                                                       class="btn btn-primary btn-lg">
                                                        <i class="fas fa-sign-in-alt mr-2"></i>Đăng nhập để đăng bài
                                                    </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php else: ?>
                                        <?php foreach($posts as $post): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if($post['is_pinned']): ?>
                                                        <i class="fas fa-thumbtack text-warning mr-2" title="Bài ghim"></i>
                                                    <?php endif; ?>
                                                    <?php if($post['is_locked']): ?>
                                                        <i class="fas fa-lock text-danger mr-2" title="Đã khóa"></i>
                                                    <?php endif; ?>
                                                    <div>
                                                        <a href="<?=base_url('client/forum-post?id='.$post['id']);?>" 
                                                           class="text-decoration-none font-weight-bold">
                                                            <?=$post['title'];?>
                                                        </a>
                                                        <?php if($post['download_link']): ?>
                                                            <i class="fas fa-download text-success ml-1" title="Có link tải"></i>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="text-dark font-weight-bold"><?=$post['username'];?></span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge badge-secondary"><?=$post['comments_count'];?></span>
                                            </td>
                                            <td>
                                                <span class="text-muted"><?=number_format($post['views']);?></span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?=time_elapsed_string($post['updated_at']);?>
                                                </small>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Phân trang -->
                            <?php 
                            $total_posts = $CMSNT->get_row("SELECT COUNT(*) as total FROM forum_posts WHERE category_id = '$category_id' AND status = 1")['total'];
                            $total_pages = ceil($total_posts / $limit);
                            
                            if($total_pages > 1): 
                            ?>
                            <nav class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <?php if($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?id=<?=$category_id;?>&page=<?=$page-1;?>">Trước</a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php for($i = max(1, $page-2); $i <= min($total_pages, $page+2); $i++): ?>
                                        <li class="page-item <?=$i == $page ? 'active' : '';?>">
                                            <a class="page-link" href="?id=<?=$category_id;?>&page=<?=$i;?>"><?=$i;?></a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if($page < $total_pages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?id=<?=$category_id;?>&page=<?=$page+1;?>">Sau</a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once(__DIR__ . '/footer.php'); ?>
