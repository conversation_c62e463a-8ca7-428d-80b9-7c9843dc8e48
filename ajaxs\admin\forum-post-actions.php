<?php
require_once(__DIR__."/../../config.php");
require_once(__DIR__."/../../libs/db.php");
require_once(__DIR__."/../../libs/helper.php");

$CMSNT = new DB();

// Set proper headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] != 'POST') {
    die(json_encode(['status' => 'error', 'message' => 'Method not allowed']));
}

// Check admin session
if (empty($_SESSION['admin_login'])) {
    die(json_encode(['status' => 'error', 'message' => 'Vui lòng đăng nhập admin!']));
}

// Verify admin permissions
$admin = $CMSNT->get_row("SELECT * FROM users WHERE token = '".check_string($_SESSION['admin_login'])."' AND admin = 1");
if (!$admin) {
    die(json_encode(['status' => 'error', 'message' => 'Không có quyền admin!']));
}

$action = isset($_POST['action']) ? check_string($_POST['action']) : '';
$post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;

if (!$post_id) {
    die(json_encode(['status' => 'error', 'message' => 'ID bài viết không hợp lệ']));
}

// Check if post exists
$post = $CMSNT->get_row("SELECT * FROM forum_posts WHERE id = '$post_id'");
if (!$post) {
    die(json_encode(['status' => 'error', 'message' => 'Bài viết không tồn tại']));
}

try {
    switch($action) {
        case 'approve':
            $update = $CMSNT->update('forum_posts', ['status' => 1], "id = '$post_id'");
            if ($update) {
                die(json_encode(['status' => 'success', 'message' => 'Duyệt bài viết thành công']));
            } else {
                die(json_encode(['status' => 'error', 'message' => 'Không thể duyệt bài viết']));
            }
            break;
            
        case 'unapprove':
            $update = $CMSNT->update('forum_posts', ['status' => 0], "id = '$post_id'");
            if ($update) {
                die(json_encode(['status' => 'success', 'message' => 'Hủy duyệt bài viết thành công']));
            } else {
                die(json_encode(['status' => 'error', 'message' => 'Không thể hủy duyệt bài viết']));
            }
            break;
            
        case 'pin':
            $update = $CMSNT->update('forum_posts', ['is_pinned' => 1], "id = '$post_id'");
            if ($update) {
                die(json_encode(['status' => 'success', 'message' => 'Ghim bài viết thành công']));
            } else {
                die(json_encode(['status' => 'error', 'message' => 'Không thể ghim bài viết']));
            }
            break;
            
        case 'unpin':
            $update = $CMSNT->update('forum_posts', ['is_pinned' => 0], "id = '$post_id'");
            if ($update) {
                die(json_encode(['status' => 'success', 'message' => 'Bỏ ghim bài viết thành công']));
            } else {
                die(json_encode(['status' => 'error', 'message' => 'Không thể bỏ ghim bài viết']));
            }
            break;
            
        case 'lock':
            $update = $CMSNT->update('forum_posts', ['is_locked' => 1], "id = '$post_id'");
            if ($update) {
                die(json_encode(['status' => 'success', 'message' => 'Khóa bài viết thành công']));
            } else {
                die(json_encode(['status' => 'error', 'message' => 'Không thể khóa bài viết']));
            }
            break;
            
        case 'unlock':
            $update = $CMSNT->update('forum_posts', ['is_locked' => 0], "id = '$post_id'");
            if ($update) {
                die(json_encode(['status' => 'success', 'message' => 'Mở khóa bài viết thành công']));
            } else {
                die(json_encode(['status' => 'error', 'message' => 'Không thể mở khóa bài viết']));
            }
            break;
            
        case 'delete':
            $delete = $CMSNT->remove('forum_posts', "id = '$post_id'");
            if ($delete) {
                // Xóa comments liên quan
                $CMSNT->remove('forum_comments', "post_id = '$post_id'");
                die(json_encode(['status' => 'success', 'message' => 'Xóa bài viết thành công']));
            } else {
                die(json_encode(['status' => 'error', 'message' => 'Không thể xóa bài viết']));
            }
            break;
            
        default:
            die(json_encode(['status' => 'error', 'message' => 'Hành động không hợp lệ']));
    }
} catch (Exception $e) {
    die(json_encode(['status' => 'error', 'message' => 'Lỗi hệ thống: ' . $e->getMessage()]));
}
?>
