<?php
require_once(__DIR__.'/../../config.php');
require_once(__DIR__.'/../../libs/db.php');
require_once(__DIR__.'/../../libs/helper.php');

$CMSNT = new DB();

// Kiểm tra đăng nhập admin
if (empty($_SESSION['admin'])) {
    die(json_encode(['status' => 'error', 'message' => 'Vui lòng đăng nhập!']));
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    die(json_encode(['status' => 'error', 'message' => 'Method not allowed']));
}

$id = (int)$_POST['id'];
$action = $_POST['action'];

// Kiểm tra bài viết có tồn tại
$post = $CMSNT->get_row("SELECT * FROM forum_posts WHERE id = '$id'");
if (!$post) {
    die(json_encode(['status' => 'error', 'message' => 'Bài viết không tồn tại']));
}

try {
    if ($action == 'lock') {
        $CMSNT->update('forum_posts', ['is_locked' => 1], " id = '$id' ");
        $message = 'Đã khóa bài viết';
    } else if ($action == 'unlock') {
        $CMSNT->update('forum_posts', ['is_locked' => 0], " id = '$id' ");
        $message = 'Đã mở khóa bài viết';
    } else {
        die(json_encode(['status' => 'error', 'message' => 'Hành động không hợp lệ']));
    }
    
    die(json_encode([
        'status' => 'success',
        'message' => $message
    ]));
} catch (Exception $e) {
    die(json_encode(['status' => 'error', 'message' => 'Có lỗi xảy ra: ' . $e->getMessage()]));
}
?>
