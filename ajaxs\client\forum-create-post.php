<?php

require_once(__DIR__.'/../../config.php');
require_once(__DIR__.'/../../libs/db.php');
require_once(__DIR__.'/../../libs/helper.php');

$CMSNT = new DB();

// Set proper JSON headers first
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

// Enable error logging for debugging
error_log("=== FORUM CREATE POST AJAX START ===");
error_log("Time: " . date('Y-m-d H:i:s'));
error_log("Request Method: " . $_SERVER['REQUEST_METHOD']);
error_log("POST Data: " . json_encode($_POST, JSON_UNESCAPED_UNICODE));
error_log("Session Data: " . json_encode($_SESSION ?? [], J<PERSON>N_UNESCAPED_UNICODE));
error_log("<PERSON><PERSON> Token: " . ($_COOKIE['token'] ?? 'Not set'));

// Kiểm tra đăng nhập qua session hoặc cookie
$getUser = null;

if (isset($_SESSION['login'])) {
    error_log("Session login found: " . $_SESSION['login']);
    $getUser = $CMSNT->get_row(" SELECT * FROM `users` WHERE `token` = '".check_string($_SESSION['login'])."' ");
    if ($getUser) {
        error_log("User authenticated from session: " . $getUser['username']);
    } else {
        error_log("Invalid session token, clearing session");
        unset($_SESSION['login']);
    }
}

// Nếu không có session hoặc session không hợp lệ, thử cookie
if (!$getUser && isset($_COOKIE["token"])) {
    error_log("No valid session, checking cookie...");
    error_log("Cookie token found: " . $_COOKIE['token']);
    $getUser = $CMSNT->get_row(" SELECT * FROM `users` WHERE `token` = '".check_string($_COOKIE['token'])."' ");
    if ($getUser) {
        $_SESSION['login'] = $getUser['token'];
        error_log("User authenticated from cookie: " . $getUser['username']);
    } else {
        error_log("Invalid cookie token");
        // Clear invalid cookie
        setcookie('token', '', time() - 3600, '/');
    }
}

// Kiểm tra cuối cùng
if (!$getUser) {
    error_log("No valid authentication found");
    die(json_encode([
        'status' => 'error',
        'message' => 'Vui lòng đăng nhập để đăng bài viết',
        'redirect' => '?module=client&action=login'
    ], JSON_UNESCAPED_UNICODE));
}

// Kiểm tra tài khoản có bị khóa không
if ($getUser['banned'] == 1) {
    error_log("User is banned: " . $getUser['username']);
    die(json_encode([
        'status' => 'error',
        'message' => 'Tài khoản của bạn đã bị khóa. Vui lòng liên hệ admin.'
    ], JSON_UNESCAPED_UNICODE));
}

error_log("User authenticated successfully: " . $getUser['username'] . " (ID: " . $getUser['id'] . ")");

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    error_log("Invalid request method: " . $_SERVER['REQUEST_METHOD']);
    die(json_encode(['status' => 'error', 'message' => 'Method not allowed']));
}

$category_id = isset($_POST['category_id']) ? (int)$_POST['category_id'] : 0;
$title = isset($_POST['title']) ? trim($_POST['title']) : '';
$content = isset($_POST['content']) ? trim($_POST['content']) : '';
$images = isset($_POST['images']) ? trim($_POST['images']) : '';
$download_link = isset($_POST['download_link']) ? trim($_POST['download_link']) : '';
$download_password = isset($_POST['download_password']) ? trim($_POST['download_password']) : '';
$is_link_hidden = isset($_POST['is_link_hidden']) ? 1 : 0;
$tags = isset($_POST['tags']) ? trim($_POST['tags']) : '';
$notify_followers = isset($_POST['notify_followers']) ? 1 : 0;
$allow_comments = isset($_POST['allow_comments']) ? 1 : 0;
$pin_request = isset($_POST['pin_request']) ? 1 : 0;

error_log("Processing post data: Category ID: $category_id, Title: $title");

// Validate dữ liệu
error_log("Validating input data...");

if (empty($category_id)) {
    error_log("Validation failed: Category ID is empty");
    die(json_encode([
        'status' => 'error',
        'message' => 'Vui lòng chọn danh mục bài viết'
    ], JSON_UNESCAPED_UNICODE));
}

if (empty($title)) {
    error_log("Validation failed: Title is empty");
    die(json_encode([
        'status' => 'error',
        'message' => 'Vui lòng nhập tiêu đề bài viết'
    ], JSON_UNESCAPED_UNICODE));
}

if (mb_strlen($title) > 255) {
    error_log("Validation failed: Title too long (" . mb_strlen($title) . " chars)");
    die(json_encode([
        'status' => 'error',
        'message' => 'Tiêu đề không được vượt quá 255 ký tự'
    ], JSON_UNESCAPED_UNICODE));
}

if (empty($content) || $content === '<p><br></p>') {
    error_log("Validation failed: Content is empty");
    die(json_encode([
        'status' => 'error',
        'message' => 'Vui lòng nhập nội dung bài viết'
    ], JSON_UNESCAPED_UNICODE));
}

if (mb_strlen($content) > 50000) {
    error_log("Validation failed: Content too long (" . mb_strlen($content) . " chars)");
    die(json_encode([
        'status' => 'error',
        'message' => 'Nội dung không được vượt quá 50,000 ký tự'
    ], JSON_UNESCAPED_UNICODE));
}

error_log("Input validation passed");

// Kiểm tra danh mục có tồn tại
error_log("Checking category existence for ID: $category_id");
$category = $CMSNT->get_row("SELECT * FROM forum_categories WHERE id = '$category_id' AND status = 1");
if (!$category) {
    error_log("Category not found or inactive: $category_id");
    die(json_encode([
        'status' => 'error',
        'message' => 'Danh mục không tồn tại hoặc đã bị vô hiệu hóa'
    ], JSON_UNESCAPED_UNICODE));
}
error_log("Category found: " . $category['name']);

// Kiểm tra quyền admin
$isAdmin = isset($getUser['admin']) && $getUser['admin'] == 1;
error_log("User admin status: " . ($isAdmin ? 'Yes' : 'No'));

if (!$isAdmin) {
    // Kiểm tra giới hạn đăng bài cho user thường
    error_log("Checking post limit for regular user...");
    $total_posts = $CMSNT->get_row("SELECT COUNT(*) as total FROM forum_posts WHERE user_id = '".$getUser['id']."'")['total'];
    error_log("User total posts: $total_posts");

    if ($total_posts > 0) {
        // Không phải bài đầu, kiểm tra điều kiện thời gian
        $last_post = $CMSNT->get_row("SELECT created_at FROM forum_posts WHERE user_id = '".$getUser['id']."' ORDER BY created_at DESC LIMIT 1");
        if ($last_post) {
            $last_post_time = strtotime($last_post['created_at']);
            $current_time = time();
            $time_diff = $current_time - $last_post_time;
            $one_week = 7 * 24 * 60 * 60; // 1 tuần tính bằng giây

            error_log("Last post time: " . $last_post['created_at']);
            error_log("Time difference: " . $time_diff . " seconds");
            error_log("One week limit: " . $one_week . " seconds");

            if ($time_diff < $one_week) {
                $remaining_time = $one_week - $time_diff;
                $remaining_days = ceil($remaining_time / (24 * 60 * 60));

                error_log("Post limit exceeded. Remaining time: $remaining_time seconds ($remaining_days days)");
                die(json_encode([
                    'status' => 'error',
                    'message' => "⏰ Bạn chỉ được đăng 1 bài viết trong vòng 1 tuần. Vui lòng đợi thêm $remaining_days ngày nữa để đăng bài tiếp theo."
                ], JSON_UNESCAPED_UNICODE));
            }
        }
    }
    error_log("Post limit check passed");
} else {
    error_log("Admin user - skipping post limit check");
}

// Làm sạch dữ liệu
$title = check_string($title);
$content = check_string($content);
$images = $images ? check_string($images) : null;
$download_link = $download_link ? check_string($download_link) : null;
$download_password = $download_password ? check_string($download_password) : null;
$tags = $tags ? check_string($tags) : null;

// Validate và xử lý tags
if ($tags) {
    $tag_array = explode(',', $tags);
    $valid_tags = [];
    foreach ($tag_array as $tag) {
        $tag = trim($tag);
        if ($tag && mb_strlen($tag) <= 50) {
            $valid_tags[] = $tag;
        }
    }
    if (count($valid_tags) > 10) {
        $valid_tags = array_slice($valid_tags, 0, 10);
    }
    $tags = !empty($valid_tags) ? implode(',', $valid_tags) : null;
}

// Validate URL download nếu có
if ($download_link && !filter_var($download_link, FILTER_VALIDATE_URL)) {
    die(json_encode(['status' => 'error', 'message' => 'Link tải không hợp lệ']));
}

// Validate URLs hình ảnh
if ($images) {
    $image_urls = explode("\n", $images);
    $valid_images = [];
    foreach ($image_urls as $url) {
        $url = trim($url);
        if ($url && filter_var($url, FILTER_VALIDATE_URL)) {
            $valid_images[] = $url;
        }
    }
    $images = !empty($valid_images) ? implode("\n", $valid_images) : null;
}

try {
    // Tạo bài viết mới - status = 0 để đợi admin duyệt (trừ admin)
    $post_status = $isAdmin ? 1 : 0; // Admin được duyệt tự động
    
    $insert = $CMSNT->insert('forum_posts', [
        'category_id' => $category_id,
        'user_id' => $getUser['id'],
        'title' => $title,
        'content' => $content,
        'images' => $images,
        'download_link' => $download_link,
        'download_password' => $download_password,
        'is_link_hidden' => $is_link_hidden,
        'tags' => $tags,
        'allow_comments' => $allow_comments,
        'status' => $post_status,
        'is_pinned' => 0, // Mặc định không ghim, admin có thể ghim sau
        'pin_requested' => $pin_request, // Lưu yêu cầu ghim để admin xem xét
        'created_at' => gmdate('Y-m-d H:i:s', time() + 7*3600),
        'updated_at' => gmdate('Y-m-d H:i:s', time() + 7*3600)
    ]);

    if ($insert) {
        $post_id = $CMSNT->lastInsertId();
        
        // Thông báo cho followers nếu được yêu cầu và user là admin
        if ($notify_followers && $isAdmin && $post_status == 1) {
            // Logic thông báo followers có thể thêm vào đây
            // Ví dụ: insert vào bảng notifications
        }
        
        // Log successful creation
        error_log("Post created successfully with ID: $post_id");
        error_log("User: " . $getUser['username'] . " (ID: " . $getUser['id'] . ")");
        error_log("Is Admin: " . ($isAdmin ? 'Yes' : 'No'));
        error_log("Post Status: " . $post_status);

        if ($isAdmin) {
            $message = '🎉 Đăng bài viết thành công! Bài viết đã được duyệt tự động.';
            if ($pin_request) {
                $message .= ' Bài viết đã được ghim.';
                // Admin có thể tự ghim bài
                $CMSNT->update('forum_posts', ['is_pinned' => 1], "id = '$post_id'");
                error_log("Post pinned automatically for admin");
            }
        } else {
            $message = '✅ Đăng bài viết thành công! Bài viết đang chờ admin duyệt và sẽ hiển thị sau khi được phê duyệt.';
            if ($pin_request) {
                $message .= ' Yêu cầu ghim bài đã được gửi đến admin.';
            }
        }

        $response = [
            'status' => 'success',
            'message' => $message,
            'post_id' => $post_id,
            'is_admin' => $isAdmin,
            'post_status' => $post_status,
            'redirect_url' => $isAdmin ?
                '?module=client&action=forum-post&id='.$post_id :
                '?module=client&action=forum'
        ];

        error_log("Response: " . json_encode($response, JSON_UNESCAPED_UNICODE));
        error_log("=== FORUM CREATE POST SUCCESS ===");

        die(json_encode($response, JSON_UNESCAPED_UNICODE));
    } else {
        error_log("Failed to insert post into database");
        error_log("Database error: " . $CMSNT->get_error());
        die(json_encode([
            'status' => 'error',
            'message' => 'Có lỗi xảy ra khi lưu bài viết vào cơ sở dữ liệu. Vui lòng thử lại.'
        ], JSON_UNESCAPED_UNICODE));
    }
} catch (Exception $e) {
    error_log("Exception in forum create post: " . $e->getMessage());
    error_log("Exception trace: " . $e->getTraceAsString());
    error_log("=== FORUM CREATE POST ERROR ===");

    die(json_encode([
        'status' => 'error',
        'message' => 'Có lỗi hệ thống xảy ra: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE));
}
?>
