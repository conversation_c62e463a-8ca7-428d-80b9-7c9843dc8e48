-- ========================================
-- FORUM SAMPLE DATA - Dữ liệu mẫu cho forum
-- ========================================

-- Thêm dữ liệu mẫu cho forum_categories nếu chưa có
INSERT IGNORE INTO `forum_categories` (`id`, `name`, `description`, `icon`, `position`, `status`, `created_at`, `updated_at`) VALUES
(1, 'Thảo luận chung', '<PERSON><PERSON><PERSON> trao đổi, thảo luận các chủ đề tổng quát về game bài', 'fas fa-comments', 1, 1, NOW(), NOW()),
(2, 'Chia sẻ mã nguồn', 'Chia sẻ và trao đổi các mã nguồn game bài, script, tool', 'fas fa-code', 2, 1, NOW(), NOW()),
(3, 'Hướng dẫn - Tutorial', '<PERSON><PERSON><PERSON> bài hướng dẫn lập trình, setup, cài đặt', 'fas fa-book', 3, 1, NOW(), NOW()),
(4, 'Báo lỗi - Bug Report', 'Báo cáo lỗi và yêu cầu hỗ trợ kỹ thuật', 'fas fa-bug', 4, 1, NOW(), NOW()),
(5, 'Chia sẻ công cụ', 'Chia sẻ các công cụ, phần mềm hữu ích cho developer', 'fas fa-tools', 5, 1, NOW(), NOW());

-- Thêm một số bài viết mẫu (chỉ nếu có user admin)
-- Lưu ý: Thay đổi user_id thành ID của admin thực tế trong hệ thống

-- Kiểm tra và thêm bài viết mẫu
INSERT IGNORE INTO `forum_posts` (`id`, `category_id`, `user_id`, `title`, `content`, `tags`, `status`, `allow_comments`, `is_pinned`, `views`, `created_at`, `updated_at`) 
SELECT 1, 1, u.id, 'Chào mừng đến với diễn đàn ShareGameBai!', 
'<h2>🎉 Chào mừng các bạn đến với diễn đàn ShareGameBai!</h2>
<p>Đây là nơi chia sẻ và trao đổi về:</p>
<ul>
<li>🎮 Mã nguồn các game bài: Poker, Blackjack, Baccarat, Tiến lên...</li>
<li>💻 Hướng dẫn lập trình web, mobile app</li>
<li>🔧 Công cụ và script hữu ích</li>
<li>🤝 Kết nối và học hỏi từ cộng đồng developer</li>
</ul>
<h3>📋 Quy định diễn đàn:</h3>
<ol>
<li><strong>Mỗi thành viên chỉ được đăng 1 bài viết trong vòng 1 tuần</strong></li>
<li><strong>Bài viết cần được admin duyệt trước khi hiển thị</strong></li>
<li>Nội dung phải có giá trị, không spam</li>
<li>Tôn trọng các thành viên khác</li>
</ol>
<p>Chúc các bạn có những trải nghiệm tuyệt vời! 🚀</p>', 
'welcome,rules,forum', 1, 1, 1, 0, NOW(), NOW()
FROM users u WHERE u.admin = 1 LIMIT 1;

INSERT IGNORE INTO `forum_posts` (`id`, `category_id`, `user_id`, `title`, `content`, `tags`, `status`, `allow_comments`, `views`, `created_at`, `updated_at`) 
SELECT 2, 2, u.id, 'Hướng dẫn chia sẻ mã nguồn hiệu quả', 
'<h2>📝 Hướng dẫn chia sẻ mã nguồn</h2>
<p>Để chia sẻ mã nguồn hiệu quả, các bạn nên:</p>
<h3>✅ Nên làm:</h3>
<ul>
<li>Mô tả rõ ràng chức năng của code</li>
<li>Cung cấp hướng dẫn cài đặt chi tiết</li>
<li>Đính kèm screenshot hoặc demo</li>
<li>Ghi rõ yêu cầu hệ thống</li>
<li>Cung cấp link tải từ nguồn uy tín</li>
</ul>
<h3>❌ Không nên:</h3>
<ul>
<li>Chia sẻ code có virus hoặc malware</li>
<li>Đăng link tải từ nguồn không rõ ràng</li>
<li>Copy paste từ nguồn khác mà không ghi nguồn</li>
<li>Spam cùng một nội dung nhiều lần</li>
</ul>
<p><strong>Lưu ý:</strong> Admin sẽ kiểm tra và duyệt bài trước khi hiển thị công khai.</p>', 
'guide,code,sharing', 1, 1, 0, 0, NOW(), NOW()
FROM users u WHERE u.admin = 1 LIMIT 1;

INSERT IGNORE INTO `forum_posts` (`id`, `category_id`, `user_id`, `title`, `content`, `tags`, `status`, `allow_comments`, `views`, `created_at`, `updated_at`) 
SELECT 3, 3, u.id, 'Cài đặt môi trường phát triển web cơ bản', 
'<h2>🛠️ Hướng dẫn cài đặt môi trường phát triển</h2>
<h3>1. Cài đặt XAMPP</h3>
<p>XAMPP là package bao gồm Apache, MySQL, PHP và Perl:</p>
<ol>
<li>Tải XAMPP từ <a href="https://www.apachefriends.org/" target="_blank">apachefriends.org</a></li>
<li>Chạy file cài đặt với quyền Administrator</li>
<li>Chọn các component: Apache, MySQL, PHP, phpMyAdmin</li>
<li>Cài đặt vào thư mục C:\xampp</li>
</ol>
<h3>2. Khởi động services</h3>
<ol>
<li>Mở XAMPP Control Panel</li>
<li>Start Apache và MySQL</li>
<li>Kiểm tra http://localhost</li>
</ol>
<h3>3. Cài đặt IDE</h3>
<p>Khuyến nghị sử dụng:</p>
<ul>
<li>Visual Studio Code (miễn phí)</li>
<li>PHPStorm (có phí)</li>
<li>Sublime Text</li>
</ul>
<p>Chúc các bạn thành công! 💪</p>', 
'tutorial,xampp,setup,development', 1, 1, 0, 0, NOW(), NOW()
FROM users u WHERE u.admin = 1 LIMIT 1;

-- Thêm một bài viết chờ duyệt để test
INSERT IGNORE INTO `forum_posts` (`id`, `category_id`, `user_id`, `title`, `content`, `tags`, `status`, `allow_comments`, `views`, `created_at`, `updated_at`) 
SELECT 4, 1, u.id, '[CHỜ DUYỆT] Bài viết test hệ thống duyệt bài', 
'<p>Đây là bài viết mẫu để test hệ thống duyệt bài của admin.</p>
<p>Bài viết này có status = 0 (chờ duyệt) để admin có thể test chức năng duyệt bài.</p>
<p>Admin có thể:</p>
<ul>
<li>✅ Duyệt bài (chuyển status = 1)</li>
<li>❌ Từ chối bài (giữ status = 0)</li>
<li>📌 Ghim bài</li>
<li>🔒 Khóa bài</li>
<li>🗑️ Xóa bài</li>
</ul>', 
'test,pending,admin', 0, 1, 0, 0, NOW(), NOW()
FROM users u WHERE u.admin = 1 LIMIT 1;

-- Cập nhật lại AUTO_INCREMENT cho bảng forum_posts
ALTER TABLE `forum_posts` AUTO_INCREMENT = 5;

-- Thêm một số comment mẫu
INSERT IGNORE INTO `forum_comments` (`id`, `post_id`, `user_id`, `content`, `status`, `created_at`, `updated_at`)
SELECT 1, 1, u.id, 'Cảm ơn admin đã tạo diễn đàn tuyệt vời! 👍', 1, NOW(), NOW()
FROM users u WHERE u.admin = 1 LIMIT 1;

INSERT IGNORE INTO `forum_comments` (`id`, `post_id`, `user_id`, `content`, `status`, `created_at`, `updated_at`)
SELECT 2, 2, u.id, 'Hướng dẫn rất chi tiết và hữu ích. Thanks! 🙏', 1, NOW(), NOW()
FROM users u WHERE u.admin = 1 LIMIT 1;

-- Cập nhật comment count cho các bài viết
UPDATE `forum_posts` SET `comments_count` = (
    SELECT COUNT(*) FROM `forum_comments` 
    WHERE `forum_comments`.`post_id` = `forum_posts`.`id` AND `forum_comments`.`status` = 1
);

-- Thêm một số view cho bài viết
UPDATE `forum_posts` SET `views` = FLOOR(RAND() * 100) + 10 WHERE `id` IN (1, 2, 3);

-- Tạo index để tối ưu performance
CREATE INDEX IF NOT EXISTS `idx_forum_posts_status` ON `forum_posts` (`status`);
CREATE INDEX IF NOT EXISTS `idx_forum_posts_category` ON `forum_posts` (`category_id`);
CREATE INDEX IF NOT EXISTS `idx_forum_posts_user` ON `forum_posts` (`user_id`);
CREATE INDEX IF NOT EXISTS `idx_forum_posts_created` ON `forum_posts` (`created_at`);
CREATE INDEX IF NOT EXISTS `idx_forum_comments_post` ON `forum_comments` (`post_id`);

-- Hiển thị thông tin sau khi chạy script
SELECT 'Forum sample data inserted successfully!' as message;
SELECT 
    (SELECT COUNT(*) FROM forum_categories WHERE status = 1) as active_categories,
    (SELECT COUNT(*) FROM forum_posts) as total_posts,
    (SELECT COUNT(*) FROM forum_posts WHERE status = 1) as approved_posts,
    (SELECT COUNT(*) FROM forum_posts WHERE status = 0) as pending_posts,
    (SELECT COUNT(*) FROM forum_comments WHERE status = 1) as total_comments;
