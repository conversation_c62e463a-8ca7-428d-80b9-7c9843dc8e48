<?php
require_once(__DIR__."/../../config.php");
require_once(__DIR__."/../../libs/db.php");
require_once(__DIR__."/../../libs/helper.php");

// Set proper headers
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] != 'POST') {
    die(json_encode(['status' => 'error', 'message' => 'Method not allowed']));
}

if (empty($_SESSION['admin_login'])) {
    die(json_encode(['status' => 'error', 'message' => 'Vui lòng đăng nhập!']));
}

// Kiểm tra quyền admin
$admin = $CMSNT->get_row("SELECT * FROM users WHERE token = '".check_string($_SESSION['admin_login'])."' AND admin = 1");
if (!$admin) {
    die(json_encode(['status' => 'error', 'message' => 'Không có quyền thực hiện!']));
}

    $action = isset($_POST['action']) ? $_POST['action'] : '';
    $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;

    if (!$post_id) {
        die(json_encode(['status' => 'error', 'message' => 'ID bài viết không hợp lệ']));
    }

    // Kiểm tra bài viết có tồn tại
    $post = $CMSNT->get_row("SELECT * FROM forum_posts WHERE id = '$post_id'");
    if (!$post) {
        die(json_encode(['status' => 'error', 'message' => 'Bài viết không tồn tại']));
    }

    if ($action == 'approve') {
        // Duyệt bài viết
        $update = $CMSNT->update("forum_posts", array(
            'status' => 1
        ), " id = '$post_id' ");

        if ($update) {
            die(json_encode(['status' => 'success', 'message' => 'Duyệt bài viết thành công']));
        } else {
            die(json_encode(['status' => 'error', 'message' => 'Duyệt bài viết thất bại']));
        }
    } else if ($action == 'unapprove') {
        // Hủy duyệt bài viết
        $update = $CMSNT->update("forum_posts", array(
            'status' => 0
        ), " id = '$post_id' ");

        if ($update) {
            die(json_encode(['status' => 'success', 'message' => 'Hủy duyệt bài viết thành công']));
        } else {
            die(json_encode(['status' => 'error', 'message' => 'Hủy duyệt bài viết thất bại']));
        }
    } else {
        die(json_encode(['status' => 'error', 'message' => 'Hành động không hợp lệ']));
    }
?>
