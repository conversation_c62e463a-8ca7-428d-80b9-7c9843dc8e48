<?php
require_once(__DIR__.'/config.php');
require_once(__DIR__.'/libs/db.php');
require_once(__DIR__.'/libs/helper.php');

$CMSNT = new DB();

echo "<h1>🔍 Debug Forum Category Issues</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: green; background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
.error { color: red; background: #ffe8e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
.info { color: blue; background: #e8f0ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
.warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>";

// Test 1: Check categories
echo "<h2>📁 Test 1: Forum Categories</h2>";
try {
    $categories = $CMSNT->get_list("SELECT * FROM forum_categories ORDER BY position ASC");
    if (count($categories) > 0) {
        echo "<div class='success'>✅ Found " . count($categories) . " categories</div>";
        echo "<table>";
        echo "<tr><th>ID</th><th>Name</th><th>Status</th><th>Posts Count</th><th>Test Link</th></tr>";
        
        foreach ($categories as $cat) {
            $posts_count = $CMSNT->num_rows("SELECT * FROM forum_posts WHERE category_id = '".$cat['id']."' AND status = 1");
            $status_text = $cat['status'] == 1 ? 'Active' : 'Inactive';
            $status_class = $cat['status'] == 1 ? 'success' : 'error';
            
            echo "<tr>";
            echo "<td>{$cat['id']}</td>";
            echo "<td>{$cat['name']}</td>";
            echo "<td><span class='$status_class'>$status_text</span></td>";
            echo "<td>$posts_count</td>";
            echo "<td><a href='?module=client&action=forum-category&id={$cat['id']}' target='_blank'>Test Category</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='error'>❌ No categories found</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}

// Test 2: Check posts
echo "<h2>📝 Test 2: Forum Posts</h2>";
try {
    $all_posts = $CMSNT->get_list("SELECT p.*, c.name as category_name, u.username 
                                   FROM forum_posts p 
                                   LEFT JOIN forum_categories c ON p.category_id = c.id 
                                   LEFT JOIN users u ON p.user_id = u.id 
                                   ORDER BY p.created_at DESC LIMIT 10");
    
    if (count($all_posts) > 0) {
        echo "<div class='success'>✅ Found " . count($all_posts) . " posts (showing latest 10)</div>";
        echo "<table>";
        echo "<tr><th>ID</th><th>Title</th><th>Category</th><th>Author</th><th>Status</th><th>Created</th></tr>";
        
        foreach ($all_posts as $post) {
            $status_text = $post['status'] == 1 ? 'Approved' : 'Pending';
            $status_class = $post['status'] == 1 ? 'success' : 'warning';
            
            echo "<tr>";
            echo "<td>{$post['id']}</td>";
            echo "<td>" . mb_substr($post['title'], 0, 50) . "...</td>";
            echo "<td>{$post['category_name']}</td>";
            echo "<td>{$post['username']}</td>";
            echo "<td><span class='$status_class'>$status_text</span></td>";
            echo "<td>" . date('d/m/Y H:i', strtotime($post['created_at'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='error'>❌ No posts found</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}

// Test 3: Check specific category
if (isset($_GET['test_category'])) {
    $test_cat_id = (int)$_GET['test_category'];
    echo "<h2>🎯 Test 3: Category ID $test_cat_id Details</h2>";
    
    try {
        $category = $CMSNT->get_row("SELECT * FROM forum_categories WHERE id = '$test_cat_id'");
        if ($category) {
            echo "<div class='info'>📁 Category: {$category['name']} (Status: {$category['status']})</div>";
            
            $posts_in_cat = $CMSNT->get_list("SELECT p.*, u.username 
                                              FROM forum_posts p 
                                              JOIN users u ON p.user_id = u.id 
                                              WHERE p.category_id = '$test_cat_id' 
                                              ORDER BY p.status DESC, p.created_at DESC");
            
            if (count($posts_in_cat) > 0) {
                echo "<div class='success'>✅ Found " . count($posts_in_cat) . " posts in this category</div>";
                echo "<table>";
                echo "<tr><th>ID</th><th>Title</th><th>Author</th><th>Status</th><th>Views</th><th>Created</th></tr>";
                
                foreach ($posts_in_cat as $post) {
                    $status_text = $post['status'] == 1 ? 'Approved' : 'Pending';
                    $status_class = $post['status'] == 1 ? 'success' : 'warning';
                    
                    echo "<tr>";
                    echo "<td>{$post['id']}</td>";
                    echo "<td>" . mb_substr($post['title'], 0, 40) . "...</td>";
                    echo "<td>{$post['username']}</td>";
                    echo "<td><span class='$status_class'>$status_text</span></td>";
                    echo "<td>{$post['views']}</td>";
                    echo "<td>" . date('d/m/Y H:i', strtotime($post['created_at'])) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<div class='warning'>⚠️ No posts found in this category</div>";
            }
        } else {
            echo "<div class='error'>❌ Category not found</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
    }
}

// Test 4: Quick actions
echo "<h2>🔧 Test 4: Quick Actions</h2>";
echo "<div class='info'>";
echo "<h3>Test Categories:</h3>";
$categories = $CMSNT->get_list("SELECT * FROM forum_categories WHERE status = 1 ORDER BY position ASC LIMIT 5");
foreach ($categories as $cat) {
    echo "<a href='?test_category={$cat['id']}' style='margin-right: 10px; padding: 5px 10px; background: #007bff; color: white; text-decoration: none; border-radius: 3px;'>Test Category: {$cat['name']}</a>";
}

echo "<h3 style='margin-top: 20px;'>Quick Links:</h3>";
echo "<a href='?module=client&action=forum' target='_blank' style='margin-right: 10px; padding: 5px 10px; background: #28a745; color: white; text-decoration: none; border-radius: 3px;'>Forum Home</a>";
echo "<a href='?module=client&action=forum-create-post' target='_blank' style='margin-right: 10px; padding: 5px 10px; background: #ffc107; color: black; text-decoration: none; border-radius: 3px;'>Create Post</a>";
echo "<a href='?module=admin&action=forum-posts' target='_blank' style='margin-right: 10px; padding: 5px 10px; background: #dc3545; color: white; text-decoration: none; border-radius: 3px;'>Admin Posts</a>";
echo "</div>";

// Test 5: Auto-fix suggestions
echo "<h2>🔨 Test 5: Auto-fix Suggestions</h2>";

// Check if there are pending posts that need approval
$pending_posts = $CMSNT->num_rows("SELECT * FROM forum_posts WHERE status = 0");
if ($pending_posts > 0) {
    echo "<div class='warning'>⚠️ Found $pending_posts pending posts that need admin approval</div>";
    echo "<p><strong>Solution:</strong> Admin should go to <a href='?module=admin&action=forum-posts' target='_blank'>Admin Panel</a> to approve posts</p>";
}

// Check if categories are empty
$empty_categories = $CMSNT->get_list("SELECT c.*, 
    (SELECT COUNT(*) FROM forum_posts p WHERE p.category_id = c.id AND p.status = 1) as posts_count
    FROM forum_categories c 
    WHERE c.status = 1 
    HAVING posts_count = 0");

if (count($empty_categories) > 0) {
    echo "<div class='warning'>⚠️ Found " . count($empty_categories) . " categories with no approved posts:</div>";
    foreach ($empty_categories as $cat) {
        echo "<p>📁 {$cat['name']} - <a href='?module=client&action=forum-create-post&category_id={$cat['id']}' target='_blank'>Create first post</a></p>";
    }
}

echo "<div class='info'>";
echo "<h3>💡 Recommendations:</h3>";
echo "<ul>";
echo "<li>If categories show no posts, create some sample posts using the forum</li>";
echo "<li>Make sure posts are approved (status = 1) by admin</li>";
echo "<li>Check that category links work properly</li>";
echo "<li>Test the forum navigation from user perspective</li>";
echo "</ul>";
echo "</div>";

echo "<div class='success'>";
echo "<p><strong>Debug completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "</div>";
?>
