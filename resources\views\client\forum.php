<?php if (!defined('IN_SITE')) {
    die('The Request Not Found');
}
$body = [
    'title' => '<PERSON><PERSON><PERSON> - ' . $CMSNT->site('title'),
    'desc'   => $CMSNT->site('description'),
    'keyword' => $CMSNT->site('keywords')
];
$body['header'] = '
<style>
body {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    min-height: 100vh;
}

.content-page {
    background: transparent;
}

.forum-hero {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    padding: 3rem 0;
    border-radius: 15px;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 8px 32px rgba(44, 62, 80, 0.3);
}

.forum-hero h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.forum-hero .lead {
    font-size: 1.2rem;
    opacity: 0.9;
}

.forum-stats-card {
    background: linear-gradient(45deg, #3498db 0%, #2980b9 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    transition: transform 0.3s ease;
    border: none;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.forum-stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}

.category-card {
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
    border: 2px solid #3498db;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    margin-bottom: 1.5rem;
}

.category-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
    border-color: #2980b9;
}

.category-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.category-header {
    background: linear-gradient(45deg, #34495e, #2c3e50);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 15px 15px 0 0;
    border: none;
}

.category-body {
    padding: 1.5rem;
    background: white;
}

.post-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.post-item:hover {
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.post-title {
    color: #2c3e50;
    font-weight: 600;
    text-decoration: none;
    font-size: 1.1rem;
}

.post-title:hover {
    color: #3498db;
    text-decoration: none;
}

.post-meta {
    font-size: 0.875rem;
    color: #7f8c8d;
}

.btn-create-post {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    color: white;
    font-weight: 700;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
}

.btn-create-post:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(46, 204, 113, 0.4);
    color: white;
}

.forum-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0,0,0,0.15);
    backdrop-filter: blur(10px);
    margin-bottom: 2rem;
    border: 1px solid rgba(255,255,255,0.2);
}

.recent-posts-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
}

.recent-posts-header {
    background: linear-gradient(45deg, #8e44ad, #9b59b6);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 15px 15px 0 0;
    border: none;
}

.badge-status {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
}

.online-users {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.breadcrumb {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.category-title {
    color: #2c3e50;
    font-weight: 800;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.category-title a {
    color: #2c3e50 !important;
    text-decoration: none !important;
}

.category-title a:hover {
    color: #3498db !important;
}

.category-desc {
    color: #34495e;
    font-size: 0.95rem;
    line-height: 1.6;
    font-weight: 500;
}

.category-stats {
    background: linear-gradient(135deg, #ecf0f1 0%, #d5dbdb 100%);
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
    border: 1px solid #bdc3c7;
}

.category-stats .badge {
    font-size: 0.9rem;
    padding: 0.4rem 0.8rem;
    font-weight: 600;
}

.category-stats small {
    color: #2c3e50;
    font-weight: 600;
}

.recent-posts-in-category {
    border-top: 1px solid rgba(0,0,0,0.1);
    padding-top: 0.75rem;
    margin-top: 0.75rem;
}

.post-item {
    transition: all 0.2s ease;
    cursor: pointer;
}

.post-item:hover {
    background: rgba(0,123,255,0.1) !important;
    transform: translateX(3px);
}

.post-item a:hover {
    color: #007bff !important;
}

.post-item img {
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-outline-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,123,255,0.3);
}

.font-weight-500 {
    font-weight: 500;
}

.recent-posts-card {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    border-radius: 15px;
    border: none;
}

.post-item {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    transition: all 0.3s ease;
    border-left: 3px solid #3498db;
}
</style>';

$body['footer'] = '';

// Kiểm tra đăng nhập
if (isset($_COOKIE["token"])) {
    $getUser = $CMSNT->get_row(" SELECT * FROM `users` WHERE `token` = '" . check_string($_COOKIE['token']) . "' ");
    if (!$getUser) {
        // Clear invalid token cookie and redirect to login
        setcookie('token', '', time() - 3600, '/');
        redirect(base_url('client/login'));
        exit();
    }
    $_SESSION['login'] = $getUser['token'];
}
if (!isset($_SESSION['login'])) {
    redirect(base_url('client/login'));
}

require_once(__DIR__.'/header.php');
require_once(__DIR__.'/sidebar.php');
?>

<div class="content-page">
    <div class="content">
        <div class="container-fluid">
            <!-- Forum Hero Section -->
            <div class="forum-container">
                <div class="forum-hero text-center">
                    <div class="container">
                        <h1 class="mb-3">
                            <i class="fas fa-comments mr-3"></i>DIỄN ĐÀN CỘNG ĐỒNG
                        </h1>
                        <p class="lead mb-4">Nơi giao lưu, học hỏi và chia sẻ kinh nghiệm về mã nguồn, lập trình</p>
                        <a href="<?=base_url('client/forum-create-post');?>" class="btn btn-create-post btn-lg">
                            <i class="fas fa-plus mr-2"></i>Đăng bài mới
                        </a>
                    </div>
                </div>

                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb" class="mt-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="<?=base_url('client/');?>"><i class="fas fa-home mr-1"></i>Trang chủ</a>
                        </li>
                        <li class="breadcrumb-item active">
                            <i class="fas fa-comments mr-1"></i>Diễn đàn
                        </li>
                    </ol>
                </nav>
            </div>

            <!-- Thống kê diễn đàn -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="forum-stats-card">
                        <i class="fas fa-newspaper fa-2x mb-2"></i>
                        <h3><?=$CMSNT->get_row("SELECT COUNT(*) as total FROM forum_posts WHERE status = 1")['total'];?></h3>
                        <p class="mb-0">Tổng bài viết</p>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="forum-stats-card">
                        <i class="fas fa-comments fa-2x mb-2"></i>
                        <h3><?=$CMSNT->get_row("SELECT COUNT(*) as total FROM forum_comments WHERE status = 1")['total'];?></h3>
                        <p class="mb-0">Tổng bình luận</p>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="forum-stats-card">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h3><?=$CMSNT->get_row("SELECT COUNT(DISTINCT user_id) as total FROM forum_posts")['total'];?></h3>
                        <p class="mb-0">Thành viên tham gia</p>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="forum-stats-card">
                        <i class="fas fa-folder fa-2x mb-2"></i>
                        <h3><?=$CMSNT->get_row("SELECT COUNT(*) as total FROM forum_categories WHERE status = 1")['total'];?></h3>
                        <p class="mb-0">Danh mục</p>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Danh sách danh mục -->
                <div class="col-lg-8">
                    <div class="card category-card">
                        <div class="card-header" style="background: linear-gradient(45deg, #007bff, #0056b3); color: white;">
                            <h5 class="mb-0">
                                <i class="fas fa-list mr-2"></i>DANH MỤC DIỄN ĐÀN
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php 
                                $categories = $CMSNT->get_list("SELECT c.*, 
                                    (SELECT COUNT(*) FROM forum_posts p WHERE p.category_id = c.id AND p.status = 1) as posts_count,
                                    (SELECT COUNT(*) FROM forum_comments cm 
                                     JOIN forum_posts p ON cm.post_id = p.id 
                                     WHERE p.category_id = c.id AND cm.status = 1) as comments_count
                                    FROM forum_categories c WHERE c.status = 1 ORDER BY c.position ASC");
                                
                                foreach($categories as $category): 
                                    // Lấy 3 bài viết mới nhất trong danh mục này
                                    $category_posts = $CMSNT->get_list("SELECT p.*, u.username, u.avatar
                                        FROM forum_posts p 
                                        JOIN users u ON p.user_id = u.id
                                        WHERE p.category_id = '".$category['id']."' AND p.status = 1 
                                        ORDER BY p.created_at DESC 
                                        LIMIT 3");
                                ?>
                                <div class="col-lg-6 mb-3">
                                    <div class="category-card h-100">
                                        <div class="card-body">
                                            <div class="d-flex align-items-start">
                                                <div class="category-icon mr-3">
                                                    <i class="<?=$category['icon'];?>"></i>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="category-title">
                                                        <a href="<?=base_url('client/forum-category?id='.$category['id']);?>" class="text-decoration-none">
                                                            <?=$category['name'];?>
                                                        </a>
                                                    </h6>
                                                    <p class="category-desc"><?=$category['description'];?></p>
                                                    
                                                    <!-- Hiển thị bài viết mới nhất -->
                                                    <?php if(count($category_posts) > 0): ?>
                                                    <div class="recent-posts-in-category mt-3">
                                                        <small class="text-muted mb-2 d-block"><i class="fas fa-file-alt mr-1"></i>Bài viết gần đây:</small>
                                                        <?php foreach($category_posts as $post): ?>
                                                        <div class="post-item mb-2 p-2" style="background: rgba(0,0,0,0.05); border-radius: 8px; border-left: 3px solid #007bff;">
                                                            <div class="d-flex align-items-center">
                                                                <img src="<?=base_url($post['avatar'] ?: 'assets/img/avatar.png');?>" 
                                                                     class="rounded-circle mr-2" 
                                                                     style="width: 24px; height: 24px; object-fit: cover;">
                                                                <div class="flex-grow-1">
                                                                    <a href="?module=client&action=forum-post&id=<?=$post['id'];?>" 
                                                                       class="text-decoration-none text-dark font-weight-500" 
                                                                       style="font-size: 0.9rem; line-height: 1.3;">
                                                                        <?=substr(htmlspecialchars($post['title']), 0, 50);?><?=(strlen($post['title']) > 50 ? '...' : '');?>
                                                                    </a>
                                                                    <div class="d-flex align-items-center mt-1">
                                                                        <small class="text-muted mr-2">
                                                                            <i class="fas fa-user mr-1"></i><?=htmlspecialchars($post['username']);?>
                                                                        </small>
                                                                        <small class="text-muted">
                                                                            <i class="fas fa-clock mr-1"></i><?=date('d/m H:i', strtotime($post['created_at']));?>
                                                                        </small>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <?php endforeach; ?>
                                                        <?php if($category['posts_count'] > 3): ?>
                                                        <div class="text-center mt-2">
                                                            <a href="?module=client&action=forum-category&id=<?=$category['id'];?>" 
                                                               class="btn btn-sm btn-outline-primary">
                                                                Xem thêm <?=($category['posts_count'] - 3);?> bài viết
                                                            </a>
                                                        </div>
                                                        <?php endif; ?>
                                                    </div>
                                                    <?php else: ?>
                                                    <div class="text-center mt-3 py-2">
                                                        <small class="text-muted">
                                                            <i class="fas fa-info-circle mr-1"></i>Chưa có bài viết nào
                                                        </small>
                                                    </div>
                                                    <?php endif; ?>
                                                    
                                                    <div class="category-stats mt-3">
                                                        <div class="row text-center">
                                                            <div class="col-6">
                                                                <small class="text-muted">Bài viết</small>
                                                                <br><span class="badge badge-primary"><?=$category['posts_count'];?></span>
                                                            </div>
                                                            <div class="col-6">
                                                                <small class="text-muted">Bình luận</small>
                                                                <br><span class="badge badge-success"><?=$category['comments_count'];?></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bài viết gần đây -->
                <div class="col-lg-4">
                    <div class="card recent-posts-card">
                        <div class="card-header text-white" style="background: linear-gradient(45deg, #fd7e14, #e63946);">
                            <h6 class="mb-0">
                                <i class="fas fa-clock mr-2"></i>BÀI VIẾT GẦN ĐÂY
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php 
                            $recent_posts = $CMSNT->get_list("SELECT p.*, c.name as category_name, u.username
                                FROM forum_posts p 
                                JOIN forum_categories c ON p.category_id = c.id
                                JOIN users u ON p.user_id = u.id
                                WHERE p.status = 1 
                                ORDER BY p.created_at DESC 
                                LIMIT 5");
                            
                            if(count($recent_posts) > 0):
                                foreach($recent_posts as $post):
                            ?>
                            <div class="post-item">
                                <h6 class="mb-1">
                                    <a href="?module=client&action=forum-post&id=<?=$post['id'];?>" class="text-decoration-none text-dark">
                                        <?=mb_substr($post['title'], 0, 40);?><?=mb_strlen($post['title']) > 40 ? '...' : '';?>
                                    </a>
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-user mr-1"></i><?=$post['username'];?>
                                    <span class="mx-2">•</span>
                                    <i class="fas fa-folder mr-1"></i><?=$post['category_name'];?>
                                </small>
                                <br>
                                <small class="text-info">
                                    <i class="fas fa-clock mr-1"></i><?=time_elapsed_string($post['created_at']);?>
                                </small>
                            </div>
                            <?php 
                                endforeach;
                            else:
                            ?>
                            <div class="text-center text-muted">
                                <i class="fas fa-inbox fa-2x mb-2"></i>
                                <p>Chưa có bài viết nào</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
        </div>
    </div>
</div>

<?php require_once(__DIR__ . '/footer.php'); ?>
