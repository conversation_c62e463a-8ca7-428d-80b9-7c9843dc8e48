<?php
// Test file để debug forum system
require_once(__DIR__.'/config.php');
require_once(__DIR__.'/libs/db.php');
require_once(__DIR__.'/libs/helper.php');

$CMSNT = new DB();

echo "<h1>🔧 Forum System Debug Test</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: green; background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
.error { color: red; background: #ffe8e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
.info { color: blue; background: #e8f0ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
.test-section { border: 1px solid #ddd; padding: 15px; margin: 15px 0; border-radius: 8px; }
</style>";

// Test 1: Database Connection
echo "<div class='test-section'>";
echo "<h2>📊 Test 1: Database Connection</h2>";
try {
    $test_query = $CMSNT->get_row("SELECT 1 as test");
    if ($test_query) {
        echo "<div class='success'>✅ Database connection: OK</div>";
    } else {
        echo "<div class='error'>❌ Database connection: FAILED</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Test 2: Check Tables
echo "<div class='test-section'>";
echo "<h2>🗃️ Test 2: Forum Tables</h2>";

$tables = ['forum_categories', 'forum_posts', 'forum_comments', 'users'];
foreach ($tables as $table) {
    try {
        $count = $CMSNT->num_rows("SELECT * FROM $table");
        echo "<div class='success'>✅ Table '$table': $count records</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Table '$table': " . $e->getMessage() . "</div>";
    }
}
echo "</div>";

// Test 3: Check Categories
echo "<div class='test-section'>";
echo "<h2>📁 Test 3: Forum Categories</h2>";
try {
    $categories = $CMSNT->get_list("SELECT * FROM forum_categories WHERE status = 1 ORDER BY position ASC");
    if (count($categories) > 0) {
        echo "<div class='success'>✅ Found " . count($categories) . " active categories:</div>";
        foreach ($categories as $cat) {
            echo "<div class='info'>📂 ID: {$cat['id']} - {$cat['name']} ({$cat['icon']})</div>";
        }
    } else {
        echo "<div class='error'>❌ No active categories found</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Categories error: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Test 4: Check Posts
echo "<div class='test-section'>";
echo "<h2>📝 Test 4: Forum Posts</h2>";
try {
    $posts = $CMSNT->get_list("SELECT p.*, c.name as category_name, u.username 
                               FROM forum_posts p 
                               LEFT JOIN forum_categories c ON p.category_id = c.id 
                               LEFT JOIN users u ON p.user_id = u.id 
                               ORDER BY p.created_at DESC LIMIT 5");
    
    echo "<div class='success'>✅ Found " . count($posts) . " recent posts:</div>";
    foreach ($posts as $post) {
        $status = $post['status'] == 1 ? 'Approved' : 'Pending';
        echo "<div class='info'>📄 ID: {$post['id']} - {$post['title']} (Status: $status, Author: {$post['username']}, Category: {$post['category_name']})</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Posts error: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Test 5: Check User Session
echo "<div class='test-section'>";
echo "<h2>👤 Test 5: User Session</h2>";
if (isset($_SESSION['login'])) {
    try {
        $user = $CMSNT->get_row("SELECT * FROM users WHERE token = '" . $_SESSION['login'] . "'");
        if ($user) {
            $admin_status = $user['admin'] == 1 ? 'Admin' : 'Regular User';
            echo "<div class='success'>✅ User logged in: {$user['username']} ($admin_status)</div>";
        } else {
            echo "<div class='error'>❌ Invalid session token</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Session error: " . $e->getMessage() . "</div>";
    }
} elseif (isset($_COOKIE['token'])) {
    try {
        $user = $CMSNT->get_row("SELECT * FROM users WHERE token = '" . check_string($_COOKIE['token']) . "'");
        if ($user) {
            $admin_status = $user['admin'] == 1 ? 'Admin' : 'Regular User';
            echo "<div class='info'>ℹ️ User from cookie: {$user['username']} ($admin_status)</div>";
        } else {
            echo "<div class='error'>❌ Invalid cookie token</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Cookie error: " . $e->getMessage() . "</div>";
    }
} else {
    echo "<div class='error'>❌ No user session found</div>";
}
echo "</div>";

// Test 6: AJAX Endpoints
echo "<div class='test-section'>";
echo "<h2>🔗 Test 6: AJAX Endpoints</h2>";
$ajax_files = [
    'ajaxs/client/forum-create-post.php',
    'ajaxs/admin/forum-post-actions.php'
];

foreach ($ajax_files as $file) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ File exists: $file</div>";
    } else {
        echo "<div class='error'>❌ File missing: $file</div>";
    }
}
echo "</div>";

// Test 7: Quick Links
echo "<div class='test-section'>";
echo "<h2>🔗 Test 7: Quick Links</h2>";
echo "<div class='info'>";
echo "<a href='?module=client&action=forum' target='_blank'>📋 Forum Home</a><br>";
echo "<a href='?module=client&action=forum-create-post' target='_blank'>✏️ Create Post</a><br>";
echo "<a href='?module=admin&action=forum-posts' target='_blank'>👨‍💼 Admin Posts</a><br>";
echo "<a href='?module=admin&action=forum-categories' target='_blank'>📁 Admin Categories</a><br>";
echo "</div>";
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>📋 Summary</h2>";
echo "<div class='info'>Test completed at: " . date('Y-m-d H:i:s') . "</div>";
echo "</div>";
?>
